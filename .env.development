# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /

# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/super-words","http://localhost:8082/super-words"],["/upload","http://localhost:3300/upload"]]

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://localhost:8082/super-words/

#后台接口父地址(必填)
VITE_GLOB_API_URL=/super-words

# 接口前缀
VITE_GLOB_API_URL_PREFIX =

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'
