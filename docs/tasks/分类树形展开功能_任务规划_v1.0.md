# 分类树形展开功能 - 任务规划

## 项目概述
- **项目名称**: 分类树形展开功能
- **版本**: v1.0
- **创建时间**: 2025-08-03
- **负责人**: Emma (产品经理)

## 任务分解

### 任务1: API接口开发
**负责人**: <PERSON> (Engineer)  
**预计工时**: 2小时  
**优先级**: 高  

**具体任务**:
1. 在 `InzLearningCategorys.api.ts` 中新增子分类查询接口
2. 实现 `getSubCategories(parentId)` 方法
3. 配置接口路径 `/sub-categories/{parentId}`
4. 添加错误处理和类型定义

**验收标准**:
- [ ] API方法正确实现
- [ ] 接口调用返回正确数据格式
- [ ] 错误处理机制完善
- [ ] TypeScript类型定义完整

### 任务2: 分类表格展开功能开发
**负责人**: <PERSON> (Engineer)  
**预计工时**: 3小时  
**优先级**: 高  

**具体任务**:
1. 修改 `InzLearningCategorysList.vue` 添加展开功能
2. 添加 `expandedRowKeys` 状态管理
3. 实现 `handleExpand` 展开事件处理
4. 配置 BasicTable 的展开属性

**技术要点**:
- 参考 `InzUserFrontList.vue` 的实现模式
- 使用 `@expand="handleExpand"` 事件绑定
- 配置 `:expandedRowKeys="expandedRowKeys"` 状态绑定

**验收标准**:
- [ ] 展开按钮正确显示
- [ ] 展开/收起状态切换正常
- [ ] 同时只能展开一个分类
- [ ] 展开事件正确触发

### 任务3: 子分类表格组件开发
**负责人**: Alex (Engineer)  
**预计工时**: 2小时  
**优先级**: 高  

**具体任务**:
1. 创建 `SubCategoriesTable.vue` 子组件
2. 实现子分类数据加载逻辑
3. 配置子分类表格列定义
4. 添加加载状态和错误处理

**技术要点**:
- 参考 `InzUserDeviceSubTable.vue` 实现模式
- 使用 `watchEffect` 监听父ID变化
- 实现 `loadData(parentId)` 数据加载方法

**文件结构**:
```
src/views/inz_learning_categorys/
├── components/
│   └── SubCategoriesTable.vue  # 新增子分类表格组件
├── InzLearningCategorysList.vue  # 主分类列表（需修改）
└── InzLearningCategorys.api.ts   # API接口（需修改）
```

**验收标准**:
- [ ] 子组件正确创建
- [ ] 数据加载逻辑正常
- [ ] 表格渲染正确
- [ ] 加载状态提示完善

### 任务4: 展开模板集成
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 中  

**具体任务**:
1. 在主表格中添加 `expandedRowRender` 模板
2. 集成 `SubCategoriesTable` 子组件
3. 配置子表格的样式和布局
4. 实现父子组件数据传递

**技术要点**:
- 使用 `<template #expandedRowRender="{ record }">` 语法
- 传递 `record.id` 作为 `parentId` 参数
- 配置子表格样式（缩进、背景色等）

**验收标准**:
- [ ] 展开区域正确显示子分类表格
- [ ] 父子组件数据传递正常
- [ ] 样式布局美观合理
- [ ] 响应式设计适配

### 任务5: 数据列定义和样式优化
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 中  

**具体任务**:
1. 在 `InzLearningCategorys.data.ts` 中定义子分类表格列
2. 优化子分类表格的显示样式
3. 添加层级缩进和视觉区分
4. 配置合适的列宽和对齐方式

**技术要点**:
- 复用主分类的列定义，适当调整
- 使用 `size="small"` 紧凑显示
- 添加左侧缩进样式
- 配置背景色区分层级

**验收标准**:
- [ ] 子分类列定义完整
- [ ] 样式美观且层级清晰
- [ ] 表格布局合理
- [ ] 视觉效果良好

### 任务6: 测试和优化
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 中  

**具体任务**:
1. 编写单元测试用例
2. 进行集成测试验证
3. 性能优化和错误处理
4. 浏览器兼容性测试

**测试用例**:
- 展开/收起功能测试
- API调用和数据加载测试
- 错误场景处理测试
- 性能和响应时间测试

**验收标准**:
- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 错误处理完善
- [ ] 浏览器兼容性良好

## 技术实施要点

### 1. 核心文件修改清单
```
需要修改的文件:
├── src/views/inz_learning_categorys/InzLearningCategorysList.vue
├── src/views/inz_learning_categorys/InzLearningCategorys.api.ts
├── src/views/inz_learning_categorys/InzLearningCategorys.data.ts

需要新增的文件:
└── src/views/inz_learning_categorys/components/SubCategoriesTable.vue
```

### 2. 关键技术点
- **展开状态管理**: 使用 `expandedRowKeys` ref 数组
- **事件处理**: 实现 `handleExpand(expanded, record)` 方法
- **子组件通信**: 通过 props 传递 parentId
- **数据加载**: 使用 `watchEffect` 监听 props 变化
- **API集成**: 调用 `/sub-categories/{parentId}` 接口

### 3. 性能优化策略
- 子分类数据本地缓存
- 懒加载机制
- 加载状态提示
- 错误重试机制

## 风险控制

### 技术风险
- **风险**: 子分类数据量过大影响性能
- **缓解**: 实施分页加载和虚拟滚动

### 兼容性风险
- **风险**: 与现有功能冲突
- **缓解**: 充分测试和渐进式集成

### 用户体验风险
- **风险**: 展开操作响应延迟
- **缓解**: 添加加载状态和超时处理

## 时间计划

| 任务 | 预计时间 | 开始时间 | 完成时间 |
|------|----------|----------|----------|
| API接口开发 | 2小时 | Day 1 上午 | Day 1 上午 |
| 主表格展开功能 | 3小时 | Day 1 上午 | Day 1 下午 |
| 子表格组件 | 2小时 | Day 1 下午 | Day 1 下午 |
| 模板集成 | 1小时 | Day 1 下午 | Day 1 下午 |
| 样式优化 | 1小时 | Day 2 上午 | Day 2 上午 |
| 测试优化 | 1小时 | Day 2 上午 | Day 2 上午 |

**总计**: 10小时，预计1.5个工作日完成

---

**任务状态**: ✅ 规划完成  
**下一步**: 开始技术架构设计和代码实现
