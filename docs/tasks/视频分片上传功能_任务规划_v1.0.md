# 视频分片上传功能 - 任务规划

## 项目概述
- **项目名称**: 视频分片上传功能
- **版本**: v1.0
- **创建时间**: 2025-08-03
- **负责人**: Emma (产品经理)

## 任务分解

### 任务1: 后端分片上传API开发
**负责人**: <PERSON> (Engineer)  
**预计工时**: 3天  
**优先级**: 高  

**具体任务**:
1. 创建视频上传相关数据库表
2. 实现视频分片上传控制器
3. 集成现有分片上传服务
4. 添加视频格式验证
5. 实现视频文件合并逻辑

**技术要点**:
- 复用现有的ChunkUploadService
- 添加视频特定的验证逻辑
- 支持多种视频格式
- 实现高效的文件合并

**验收标准**:
- [ ] 视频上传API接口完整实现
- [ ] 支持mp4、avi、mov等主流格式
- [ ] 分片上传和合并功能正常
- [ ] 错误处理机制完善
- [ ] API文档完整

### 任务2: 前端分片上传组件开发
**负责人**: <PERSON> (Engineer)  
**预计工时**: 4天  
**优先级**: 高  

**具体任务**:
1. 创建ChunkVideoUpload组件
2. 实现文件分片和MD5计算
3. 开发上传进度显示
4. 实现暂停/恢复/取消功能
5. 添加断点续传支持

**技术要点**:
- 使用Web Workers进行MD5计算
- 实现并发分片上传控制
- 添加上传状态管理
- 优化用户交互体验

**验收标准**:
- [ ] 分片上传组件功能完整
- [ ] 进度显示准确实时
- [ ] 暂停恢复功能正常
- [ ] 断点续传工作正常
- [ ] 用户体验流畅

### 任务3: 视频表单集成
**负责人**: Alex (Engineer)  
**预计工时**: 1天  
**优先级**: 中  

**具体任务**:
1. 修改视频表单配置
2. 集成ChunkVideoUpload组件
3. 实现智能上传策略
4. 更新表单验证规则
5. 优化表单布局

**技术要点**:
- 替换现有的JUpload组件
- 实现大小文件智能切换
- 保持表单数据格式兼容
- 优化组件加载性能

**验收标准**:
- [ ] 视频表单正常使用分片上传
- [ ] 小文件仍使用普通上传
- [ ] 表单验证规则正确
- [ ] 数据提交格式正确
- [ ] 向后兼容性良好

### 任务4: 数据库表结构设计
**负责人**: Bob (Architect)  
**预计工时**: 0.5天  
**优先级**: 高  

**具体任务**:
1. 设计视频上传任务表
2. 设计视频分片详情表
3. 添加视频特定字段
4. 创建数据库索引
5. 编写SQL初始化脚本

**技术要点**:
- 基于现有分片上传表结构
- 添加视频特定字段（格式、时长、分辨率）
- 优化查询性能
- 考虑数据清理策略

**验收标准**:
- [ ] 数据库表结构设计合理
- [ ] 索引配置优化查询性能
- [ ] SQL脚本可正常执行
- [ ] 支持数据迁移
- [ ] 文档完整清晰

### 任务5: 测试和优化
**负责人**: Alex (Engineer) + David (Data Analyst)  
**预计工时**: 2天  
**优先级**: 中  

**具体任务**:
1. 编写单元测试和集成测试
2. 进行性能测试和优化
3. 测试各种视频格式
4. 验证断点续传功能
5. 进行用户体验测试

**技术要点**:
- 使用Playwright进行E2E测试
- 测试大文件上传性能
- 验证内存使用情况
- 测试网络异常场景

**验收标准**:
- [ ] 测试覆盖率达到80%以上
- [ ] 性能指标满足要求
- [ ] 各种异常场景处理正确
- [ ] 用户体验测试通过
- [ ] 文档和示例完整

## 技术实现方案

### 1. 后端API设计

#### 视频上传控制器
```java
@RestController
@RequestMapping("/sys/video-upload")
@Slf4j
public class VideoChunkUploadController {
    
    @Autowired
    private IChunkUploadService chunkUploadService;
    
    @PostMapping("/init")
    public Result<UploadTask> initVideoUpload(@RequestBody InitVideoUploadRequest request) {
        // 验证视频格式
        if (!isValidVideoFormat(request.getFileName())) {
            return Result.error("不支持的视频格式");
        }
        
        // 检查文件大小
        if (request.getFileSize() > MAX_VIDEO_SIZE) {
            return Result.error("视频文件过大，最大支持5GB");
        }
        
        // 创建上传任务
        UploadTask task = chunkUploadService.initUpload(request);
        return Result.ok(task);
    }
    
    @PostMapping("/chunk")
    public Result<String> uploadVideoChunk(
            @RequestParam("file") MultipartFile file,
            @RequestParam("taskId") String taskId,
            @RequestParam("chunkIndex") Integer chunkIndex,
            @RequestParam(value = "chunkHash", required = false) String chunkHash) {
        
        String storagePath = chunkUploadService.uploadChunk(taskId, chunkIndex, file, chunkHash);
        return Result.ok(storagePath);
    }
    
    @PostMapping("/merge")
    public Result<String> mergeVideoChunks(@RequestParam("taskId") String taskId) {
        String finalUrl = chunkUploadService.mergeChunks(taskId);
        
        // 生成视频缩略图
        generateVideoThumbnail(finalUrl);
        
        return Result.ok(finalUrl);
    }
}
```

### 2. 前端组件设计

#### ChunkVideoUpload组件
```vue
<template>
  <div class="chunk-video-upload">
    <div v-if="!uploading" class="upload-area" @click="selectFile" @drop="handleDrop" @dragover.prevent>
      <a-upload-dragger :show-upload-list="false" :before-upload="beforeUpload">
        <p class="ant-upload-drag-icon">
          <Icon icon="ant-design:video-camera-outlined" size="48" />
        </p>
        <p class="ant-upload-text">点击或拖拽视频文件到此区域上传</p>
        <p class="ant-upload-hint">支持mp4、avi、mov等格式，最大5GB</p>
      </a-upload-dragger>
    </div>
    
    <div v-else class="upload-progress-container">
      <div class="file-info">
        <Icon icon="ant-design:video-camera-outlined" />
        <span class="file-name">{{ fileName }}</span>
        <span class="file-size">{{ formatFileSize(fileSize) }}</span>
      </div>
      
      <a-progress 
        :percent="Math.round(uploadProgress)" 
        :status="progressStatus"
        :stroke-color="progressColor"
      />
      
      <div class="upload-stats">
        <span>上传速度: {{ uploadSpeed }}</span>
        <span>已上传: {{ formatFileSize(uploadedSize) }}</span>
        <span>剩余时间: {{ remainingTime }}</span>
      </div>
      
      <div class="upload-controls">
        <a-button @click="pauseUpload" v-if="!paused && uploading" type="default">
          <Icon icon="ant-design:pause-outlined" />
          暂停
        </a-button>
        <a-button @click="resumeUpload" v-if="paused" type="primary">
          <Icon icon="ant-design:play-circle-outlined" />
          恢复
        </a-button>
        <a-button @click="cancelUpload" danger>
          <Icon icon="ant-design:close-outlined" />
          取消
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { ChunkUploader } from './chunk-uploader';

// 组件属性
interface Props {
  value?: string;
  maxSize?: number; // MB
  accept?: string;
  chunkSize?: number; // MB
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 5120, // 5GB
  accept: '.mp4,.avi,.mov,.wmv,.flv,.webm',
  chunkSize: 5, // 5MB
});

// 响应式数据
const uploading = ref(false);
const paused = ref(false);
const fileName = ref('');
const fileSize = ref(0);
const uploadProgress = ref(0);
const uploadSpeed = ref('0 KB/s');
const uploadedSize = ref(0);
const remainingTime = ref('计算中...');

// 上传器实例
let uploader: ChunkUploader | null = null;

// 计算属性
const progressStatus = computed(() => {
  if (paused.value) return 'normal';
  if (uploadProgress.value === 100) return 'success';
  return 'active';
});

const progressColor = computed(() => {
  if (paused.value) return '#faad14';
  if (uploadProgress.value === 100) return '#52c41a';
  return '#1890ff';
});

// 方法
const beforeUpload = (file: File) => {
  // 验证文件格式
  if (!isValidVideoFile(file)) {
    message.error('请选择有效的视频文件');
    return false;
  }
  
  // 验证文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    message.error(`文件大小不能超过${props.maxSize}MB`);
    return false;
  }
  
  startUpload(file);
  return false; // 阻止默认上传
};

const startUpload = async (file: File) => {
  uploading.value = true;
  fileName.value = file.name;
  fileSize.value = file.size;
  
  uploader = new ChunkUploader({
    chunkSize: props.chunkSize * 1024 * 1024,
    onProgress: (progress) => {
      uploadProgress.value = progress.percentage;
      uploadSpeed.value = formatSpeed(progress.speed);
      uploadedSize.value = progress.uploadedSize;
      remainingTime.value = formatTime(progress.remainingTime);
    },
    onSuccess: (url) => {
      message.success('视频上传成功');
      emit('update:value', url);
      emit('success', url);
      uploading.value = false;
    },
    onError: (error) => {
      message.error(`上传失败: ${error.message}`);
      uploading.value = false;
    }
  });
  
  try {
    await uploader.upload(file, 'video', 'local');
  } catch (error) {
    console.error('上传失败:', error);
  }
};

const pauseUpload = () => {
  if (uploader) {
    uploader.pause();
    paused.value = true;
  }
};

const resumeUpload = () => {
  if (uploader) {
    uploader.resume();
    paused.value = false;
  }
};

const cancelUpload = () => {
  if (uploader) {
    uploader.cancel();
    uploading.value = false;
    paused.value = false;
    uploadProgress.value = 0;
  }
};

// 工具函数
const isValidVideoFile = (file: File): boolean => {
  const validTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
  return validTypes.includes(file.type) || props.accept.includes(file.name.split('.').pop()?.toLowerCase() || '');
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatSpeed = (bytesPerSecond: number): string => {
  return formatFileSize(bytesPerSecond) + '/s';
};

const formatTime = (seconds: number): string => {
  if (!isFinite(seconds)) return '计算中...';
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

// 事件
const emit = defineEmits(['update:value', 'success', 'error']);
</script>
```

### 3. 表单集成

#### 修改视频表单配置
```typescript
// InzLearningVideos.data.ts
{
  label: '视频文件',
  field: 'videoUrl',
  component: 'ChunkVideoUpload', // 使用新的分片上传组件
  componentProps: {
    maxSize: 5120, // 5GB
    accept: '.mp4,.avi,.mov,.wmv,.flv,.webm',
    chunkSize: 5, // 5MB分片
  },
  dynamicRules: ({ model, schema }) => {
    return [{ required: true, message: '请上传视频文件!' }];
  },
},
```

## 测试计划

### 1. 功能测试
- 各种视频格式上传测试
- 大文件分片上传测试
- 断点续传功能测试
- 暂停恢复功能测试
- 错误处理测试

### 2. 性能测试
- 大文件上传性能测试
- 并发上传测试
- 内存使用测试
- 网络异常测试

### 3. 兼容性测试
- 不同浏览器兼容性
- 移动端适配测试
- 不同网络环境测试

## 风险评估

### 高风险
- 大文件上传可能导致服务器压力
- 网络不稳定影响上传成功率

### 中风险
- 浏览器兼容性问题
- 存储空间不足

### 低风险
- 用户体验适应问题
- 小文件上传性能影响

## 上线计划

### 开发阶段 (10天)
- Day 1-3: 后端API开发
- Day 4-7: 前端组件开发
- Day 8: 表单集成
- Day 9-10: 测试和优化

### 测试阶段 (3天)
- 功能测试和性能测试
- 用户体验测试
- 兼容性测试

### 上线阶段 (2天)
- 灰度发布和监控
- 全量发布和验证
