# 模块名称显示功能 - 任务规划

## 项目概述
- **项目名称**: 模块名称显示功能
- **版本**: v1.0
- **创建时间**: 2025-08-03
- **负责人**: Emma (产品经理)

## 任务分解

### 任务1: 模块查询API接口扩展
**负责人**: <PERSON> (Engineer)  
**预计工时**: 1小时  
**优先级**: 高  

**具体任务**:
1. 在 `InzLearningModules.api.ts` 中添加 queryById 接口
2. 实现 `queryModuleById(id)` 方法
3. 配置正确的接口路径和参数
4. 添加错误处理和类型定义

**技术要点**:
- 接口路径: `/inz_learning_modules/inzLearningModules/queryById`
- 参数格式: `{ id: moduleId }`
- 返回格式: 标准模块对象

**验收标准**:
- [ ] API方法正确实现
- [ ] 接口调用返回正确的模块数据
- [ ] 错误处理机制完善
- [ ] TypeScript类型定义完整

### 任务2: 模块名称渲染组件开发
**负责人**: <PERSON> (Engineer)  
**预计工时**: 2小时  
**优先级**: 高  

**具体任务**:
1. 创建 `ModuleNameRenderer.vue` 组件
2. 实现异步模块信息加载逻辑
3. 添加加载状态和错误处理
4. 实施模块信息缓存机制

**技术要点**:
- 使用 Vue 3 Composition API
- 实现 props 接收 moduleId
- 使用 watchEffect 监听 moduleId 变化
- 实施 Map 结构的缓存机制

**文件位置**:
```
src/views/inz_learning_categorys/components/ModuleNameRenderer.vue
```

**验收标准**:
- [ ] 组件正确创建和渲染
- [ ] 异步加载逻辑正常
- [ ] 加载状态显示正确
- [ ] 缓存机制有效工作
- [ ] 错误处理完善

### 任务3: 分类表格列配置修改
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 高  

**具体任务**:
1. 修改 `InzLearningCategorys.data.ts` 中的模块列配置
2. 将列标题从"所属模块ID"改为"所属模块"
3. 配置 customRender 使用 ModuleNameRenderer 组件
4. 调整列宽和样式

**技术要点**:
- 使用 Vue 的 h() 函数渲染组件
- 传递 moduleId 作为 props
- 配置合适的列宽和对齐方式

**验收标准**:
- [ ] 列标题正确更新
- [ ] customRender 配置正确
- [ ] 模块名称正确显示
- [ ] 样式和布局合理

### 任务4: 子分类表格同步更新
**负责人**: Alex (Engineer)  
**预计工时**: 0.5小时  
**优先级**: 中  

**具体任务**:
1. 检查子分类表格是否包含模块字段
2. 如果包含，同步应用模块名称显示
3. 确保主表格和子表格的一致性

**验收标准**:
- [ ] 子分类表格模块显示一致
- [ ] 功能正常工作
- [ ] 样式保持统一

### 任务5: 性能优化和缓存机制
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 中  

**具体任务**:
1. 实施全局模块信息缓存
2. 优化重复请求的避免机制
3. 添加缓存过期和清理策略
4. 性能监控和优化

**技术要点**:
- 使用 Map 或 WeakMap 实现缓存
- 实施请求去重机制
- 添加缓存大小限制
- 监控API调用频率

**验收标准**:
- [ ] 缓存机制正常工作
- [ ] 重复请求被有效避免
- [ ] 性能指标达标
- [ ] 内存使用合理

### 任务6: 测试和文档
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 中  

**具体任务**:
1. 编写单元测试用例
2. 进行集成测试验证
3. 性能测试和优化
4. 更新技术文档

**测试用例**:
- 模块名称正确显示测试
- 异步加载状态测试
- 错误处理测试
- 缓存机制测试
- 性能压力测试

**验收标准**:
- [ ] 所有测试用例通过
- [ ] 性能指标达标
- [ ] 文档更新完整
- [ ] 代码质量良好

## 技术实施要点

### 1. 核心文件修改清单
```
需要修改的文件:
├── src/views/inz_learning_modules/InzLearningModules.api.ts
├── src/views/inz_learning_categorys/InzLearningCategorys.data.ts
└── src/views/inz_learning_categorys/components/SubCategoriesTable.vue (如需要)

需要新增的文件:
└── src/views/inz_learning_categorys/components/ModuleNameRenderer.vue
```

### 2. 关键技术点
- **API接口**: 新增 queryModuleById 方法
- **组件渲染**: 使用 h() 函数在 customRender 中渲染 Vue 组件
- **异步加载**: 使用 watchEffect 和 ref 管理异步状态
- **缓存机制**: 使用 Map 结构实现模块信息缓存
- **错误处理**: 完善的加载失败降级显示

### 3. 性能优化策略
- 模块信息本地缓存
- 请求去重机制
- 懒加载和按需加载
- 合理的缓存过期策略

## 实现示例

### API接口扩展
```typescript
// InzLearningModules.api.ts
enum Api {
  // 现有接口...
  queryById = '/inz_learning_modules/inzLearningModules/queryById',
}

export const queryModuleById = (id: string) => {
  return defHttp.get({
    url: Api.queryById,
    params: { id },
  });
};
```

### 模块名称渲染组件
```vue
<!-- ModuleNameRenderer.vue -->
<template>
  <span v-if="loading" class="module-loading">
    <Spin size="small" />
  </span>
  <span v-else-if="moduleName" class="module-name">
    {{ moduleName }}
  </span>
  <span v-else class="module-error">
    未知模块
  </span>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { Spin } from 'ant-design-vue';
import { queryModuleById } from '../../inz_learning_modules/InzLearningModules.api';

interface Props {
  moduleId: string;
}

const props = defineProps<Props>();

const loading = ref(false);
const moduleName = ref('');

// 全局模块缓存
const moduleCache = new Map<string, string>();

watchEffect(async () => {
  if (!props.moduleId) return;
  
  // 检查缓存
  if (moduleCache.has(props.moduleId)) {
    moduleName.value = moduleCache.get(props.moduleId)!;
    return;
  }
  
  loading.value = true;
  try {
    const res = await queryModuleById(props.moduleId);
    if (res.success && res.result) {
      const name = res.result.moduleName || '未知模块';
      moduleName.value = name;
      moduleCache.set(props.moduleId, name);
    }
  } catch (error) {
    console.error('获取模块信息失败:', error);
    moduleName.value = '';
  } finally {
    loading.value = false;
  }
});
</script>

<style scoped>
.module-loading {
  color: #999;
}

.module-name {
  color: #333;
}

.module-error {
  color: #999;
  font-style: italic;
}
</style>
```

### 表格列配置
```typescript
// InzLearningCategorys.data.ts
import { h } from 'vue';
import ModuleNameRenderer from './components/ModuleNameRenderer.vue';

export const columns: BasicColumn[] = [
  {
    title: '所属模块',
    align: 'center',
    dataIndex: 'moduleId',
    width: 150,
    customRender: ({ text: moduleId }) => {
      return h(ModuleNameRenderer, { moduleId });
    },
  },
  // 其他列...
];
```

## 风险控制

### 技术风险
- **风险**: 大量模块ID导致API请求过多
- **缓解**: 实施智能缓存和请求去重

### 性能风险
- **风险**: 异步加载影响表格渲染性能
- **缓解**: 优化加载策略和缓存机制

### 用户体验风险
- **风险**: 加载延迟影响用户体验
- **缓解**: 添加加载状态和合理的超时处理

## 时间计划

| 任务 | 预计时间 | 开始时间 | 完成时间 |
|------|----------|----------|----------|
| API接口扩展 | 1小时 | Day 1 上午 | Day 1 上午 |
| 渲染组件开发 | 2小时 | Day 1 上午 | Day 1 下午 |
| 表格配置修改 | 1小时 | Day 1 下午 | Day 1 下午 |
| 子表格同步 | 0.5小时 | Day 1 下午 | Day 1 下午 |
| 性能优化 | 1小时 | Day 1 下午 | Day 1 下午 |
| 测试文档 | 1小时 | Day 2 上午 | Day 2 上午 |

**总计**: 6.5小时，预计1个工作日完成

---

**任务状态**: ✅ 规划完成  
**下一步**: 开始API接口扩展和组件开发
