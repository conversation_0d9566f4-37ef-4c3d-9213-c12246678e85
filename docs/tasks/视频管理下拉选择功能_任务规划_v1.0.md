# 视频管理下拉选择功能 - 任务规划

## 项目概述
- **项目名称**: 视频管理下拉选择功能
- **版本**: v1.0
- **创建时间**: 2025-08-03
- **负责人**: Emma (产品经理)

## 任务分解

### 任务1: API接口准备和扩展
**负责人**: <PERSON> (Engineer)  
**预计工时**: 1小时  
**优先级**: 高  

**具体任务**:
1. 在 `InzLearningModules.api.ts` 中添加获取模块列表的方法
2. 在 `InzLearningCategorys.api.ts` 中添加获取分类列表的方法
3. 支持按模块ID筛选分类的接口
4. 添加必要的类型定义和错误处理

**技术要点**:
- 模块列表接口: `/inz_learning_modules/inzLearningModules/list`
- 分类列表接口: `/inz_learning_categorys/inzLearningCategorys/list`
- 支持分页和搜索参数
- 返回格式标准化

**验收标准**:
- [ ] 模块列表API方法正确实现
- [ ] 分类列表API方法正确实现
- [ ] 支持按模块ID筛选分类
- [ ] API调用返回正确的数据格式
- [ ] 错误处理机制完善

### 任务2: 视频表单组件改造
**负责人**: Alex (Engineer)  
**预计工时**: 2小时  
**优先级**: 高  

**具体任务**:
1. 修改 `InzLearningVideos.data.ts` 中的表单配置
2. 将模块ID字段改为ApiSelect组件
3. 将分类ID字段改为ApiSelect组件
4. 配置下拉选择的数据源和显示字段
5. 添加搜索和筛选功能

**技术要点**:
- 使用 `ApiSelect` 组件替换 `Input` 组件
- 配置 `labelField` 和 `valueField`
- 启用搜索功能 (`showSearch: true`)
- 设置合适的占位符文本

**验收标准**:
- [ ] 模块选择下拉框正常显示
- [ ] 分类选择下拉框正常显示
- [ ] 下拉选项显示名称，提交ID值
- [ ] 搜索功能正常工作
- [ ] 表单验证规则正常

### 任务3: 级联选择功能开发
**负责人**: Alex (Engineer)  
**预计工时**: 1.5小时  
**优先级**: 中  

**具体任务**:
1. 实现模块选择后自动筛选分类
2. 添加分类选择的依赖关系
3. 实现清空模块时重置分类选择
4. 优化数据加载和缓存机制

**技术要点**:
- 使用 `watchEffect` 监听模块选择变化
- 动态更新分类下拉选项
- 实施智能缓存避免重复请求
- 处理异步加载状态

**验收标准**:
- [ ] 选择模块后分类自动筛选
- [ ] 清空模块选择后分类恢复全部选项
- [ ] 级联选择响应速度良好
- [ ] 缓存机制正常工作
- [ ] 异步加载状态正确显示

### 任务4: 视频文件上传功能增强
**负责人**: Alex (Engineer)  
**预计工时**: 1小时  
**优先级**: 中  

**具体任务**:
1. 优化视频文件URL字段的输入方式
2. 考虑添加文件上传组件
3. 改善视频文件的管理和显示
4. 添加文件格式验证

**技术要点**:
- 评估使用 `JUpload` 组件替换文本输入
- 支持多文件上传和JSON格式存储
- 添加文件类型和大小限制
- 优化文件预览功能

**验收标准**:
- [ ] 视频文件上传功能正常
- [ ] 支持多种视频格式
- [ ] 文件大小和格式验证
- [ ] 上传进度和状态显示
- [ ] 文件预览功能

### 任务5: 表格列显示优化
**负责人**: Alex (Engineer)  
**预计工时**: 0.5小时  
**优先级**: 低  

**具体任务**:
1. 在视频列表中添加模块名称和分类名称列
2. 复用已有的 `ModuleNameRenderer` 组件
3. 创建分类名称渲染组件
4. 优化表格列的显示顺序

**技术要点**:
- 复用模块名称显示组件
- 创建类似的分类名称显示组件
- 使用 `customRender` 进行异步渲染
- 保持表格性能和用户体验

**验收标准**:
- [ ] 视频列表显示模块名称
- [ ] 视频列表显示分类名称
- [ ] 异步加载性能良好
- [ ] 错误处理机制完善
- [ ] 表格列宽度合理

## 技术实现方案

### 1. API接口扩展

#### 模块列表API
```typescript
// InzLearningModules.api.ts
export const getModuleList = (params = {}) => {
  return defHttp.get({
    url: Api.list,
    params: {
      pageNo: 1,
      pageSize: 1000, // 获取所有模块
      ...params,
    },
  });
};
```

#### 分类列表API
```typescript
// InzLearningCategorys.api.ts
export const getCategoryList = (params = {}) => {
  return defHttp.get({
    url: Api.list,
    params: {
      pageNo: 1,
      pageSize: 1000, // 获取所有分类
      ...params,
    },
  });
};

// 按模块ID获取分类
export const getCategoryListByModule = (moduleId: string) => {
  return getCategoryList({ moduleId });
};
```

### 2. 表单配置改造

```typescript
// InzLearningVideos.data.ts
export const formSchema: FormSchema[] = [
  {
    label: '所属模块',
    field: 'moduleId',
    component: 'ApiSelect',
    componentProps: {
      api: getModuleList,
      labelField: 'moduleName',
      valueField: 'id',
      placeholder: '请选择模块',
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.toLowerCase().includes(input.toLowerCase());
      },
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择所属模块!' }];
    },
  },
  {
    label: '所属分类',
    field: 'categoryId',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: formModel.moduleId ? 
          () => getCategoryListByModule(formModel.moduleId) : 
          getCategoryList,
        labelField: 'categoryName',
        valueField: 'id',
        placeholder: '请选择分类',
        showSearch: true,
        disabled: !formModel.moduleId,
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择所属分类!' }];
    },
  },
  // 其他字段保持不变...
];
```

### 3. 级联选择实现

```typescript
// 在表单组件中添加监听
watchEffect(() => {
  if (formModel.moduleId) {
    // 模块改变时清空分类选择
    formModel.categoryId = undefined;
    // 重新加载分类列表
    reloadCategoryOptions();
  }
});
```

## 测试计划

### 1. 功能测试
- 模块下拉选择功能
- 分类下拉选择功能
- 级联选择功能
- 搜索和筛选功能
- 表单提交和数据验证

### 2. 性能测试
- 大数据量下拉选择性能
- 级联选择响应速度
- 缓存机制效果
- 页面加载时间

### 3. 兼容性测试
- 现有数据兼容性
- 不同浏览器兼容性
- 移动端适配

### 4. 用户体验测试
- 操作流程流畅性
- 错误提示友好性
- 加载状态显示
- 界面响应速度

## 风险评估

### 高风险
- API接口数据格式不兼容
- 大数据量时性能问题

### 中风险
- 级联选择逻辑复杂性
- 缓存机制实现难度

### 低风险
- UI组件兼容性问题
- 表单验证规则调整

## 上线计划

### 开发阶段 (5天)
- Day 1: API接口准备
- Day 2-3: 表单组件改造
- Day 4: 级联选择功能
- Day 5: 测试和优化

### 测试阶段 (2天)
- 功能测试和性能测试
- 用户体验测试和优化

### 上线阶段 (1天)
- 灰度发布和监控
- 全量发布和验证
