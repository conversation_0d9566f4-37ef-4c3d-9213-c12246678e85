# 分类树形展开功能 PRD

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-03
- **负责人**: Emma (产品经理)
- **项目**: 继续深造-分类管理系统
- **功能模块**: 分类列表树形展开

## 2. 背景与问题陈述

### 2.1 当前问题
- 分类管理页面只能平铺显示所有分类，无法直观展示父子关系
- 用户无法快速查看某个分类下的子分类
- 缺乏层级结构的可视化展示，影响管理效率

### 2.2 业务价值
- 提升分类管理的用户体验
- 直观展示分类层级关系
- 减少用户查找子分类的操作步骤
- 与现有用户表展开功能保持交互一致性

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
- **主要目标**: 实现分类表的树形展开/收起功能
- **用户体验目标**: 提供直观的层级分类浏览体验
- **技术目标**: 复用现有展开组件架构，确保代码一致性

### 3.2 关键结果 (Key Results)
- 分类表支持点击展开按钮查看子分类
- 展开/收起操作响应时间 < 500ms
- 子分类数据正确加载并显示
- 与现有用户表展开功能交互体验一致

### 3.3 反向指标 (Counter Metrics)
- 页面加载时间不增加超过200ms
- 不影响现有分类管理功能
- 不增加额外的网络请求负担

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 系统管理员
- **次要用户**: 内容管理员
- **使用场景**: 分类管理、内容组织

### 4.2 用户故事
**作为** 系统管理员  
**我希望** 在分类列表中点击展开按钮查看子分类  
**以便于** 快速了解分类的层级结构和子分类内容

**作为** 内容管理员  
**我希望** 能够直观地看到分类的父子关系  
**以便于** 更好地组织和管理学习内容

## 5. 功能规格详述

### 5.1 核心功能
1. **展开按钮显示**
   - 在分类名称左侧显示展开/收起按钮
   - 只有存在子分类的分类才显示展开按钮
   - 按钮状态：展开(+) / 收起(-)

2. **子分类展示**
   - 点击展开按钮调用 `/sub-categories/{parentId}` 接口
   - 在当前行下方展示子分类列表
   - 子分类以嵌套表格形式显示

3. **交互行为**
   - 支持展开/收起切换
   - 同时只能展开一个分类的子分类
   - 展开时显示加载状态

### 5.2 技术实现规格
1. **组件复用**
   - 复用 BasicTable 的 expandedRowKeys 和 handleExpand 机制
   - 参考 InzUserFrontList.vue 的实现模式
   - 使用 expandedRowRender 模板渲染子分类

2. **API集成**
   - 新增 `getSubCategories(parentId)` API调用
   - 接口路径: `/sub-categories/{parentId}`
   - 返回格式: 标准分页结果格式

3. **状态管理**
   - 使用 expandedRowKeys 管理展开状态
   - 使用 loading 状态管理加载状态
   - 子分类数据本地缓存优化

### 5.3 UI/UX设计
1. **展开按钮样式**
   - 使用 Ant Design 的 Icon 组件
   - 展开: `ant-design:plus-square-outlined`
   - 收起: `ant-design:minus-square-outlined`

2. **子分类表格样式**
   - 使用 `size="small"` 紧凑显示
   - 添加左侧缩进表示层级关系
   - 背景色略有区别以区分层级

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 分类表展开/收起功能
- 子分类数据加载和显示
- 展开状态管理
- 加载状态提示
- 与现有分类管理功能的集成

### 6.2 排除功能 (Out of Scope)
- 多级嵌套展开（仅支持一级子分类展开）
- 子分类的编辑/删除操作
- 拖拽排序功能
- 批量操作子分类

## 7. 依赖与风险

### 7.1 内部依赖
- BasicTable 组件的展开功能支持
- 现有分类API的稳定性
- `/sub-categories/{parentId}` 接口的实现

### 7.2 外部依赖
- 后端子分类查询接口
- 数据库分类层级关系的正确性

### 7.3 潜在风险
- **技术风险**: 子分类数据量过大影响性能
- **业务风险**: 分类层级关系数据不一致
- **用户体验风险**: 展开操作响应延迟

### 7.4 风险缓解策略
- 实施分页加载机制
- 添加数据校验和错误处理
- 实施加载状态提示和超时处理

## 8. 发布初步计划

### 8.1 开发阶段
1. **Phase 1**: API接口开发和测试 (1天)
2. **Phase 2**: 前端组件开发 (1天)
3. **Phase 3**: 集成测试和优化 (0.5天)

### 8.2 测试计划
- 单元测试: 展开/收起逻辑测试
- 集成测试: API调用和数据渲染测试
- 用户体验测试: 交互流程验证

### 8.3 上线策略
- 灰度发布: 先在测试环境验证
- 全量发布: 确认无问题后全量上线
- 监控指标: 接口响应时间、错误率、用户操作成功率

## 9. 验收标准

### 9.1 功能验收
- [ ] 分类表显示展开按钮（仅有子分类的分类）
- [ ] 点击展开按钮成功调用子分类API
- [ ] 子分类数据正确显示在展开区域
- [ ] 展开/收起状态切换正常
- [ ] 同时只能展开一个分类

### 9.2 性能验收
- [ ] 展开操作响应时间 < 500ms
- [ ] 页面加载时间增加 < 200ms
- [ ] 子分类数据加载成功率 > 99%

### 9.3 兼容性验收
- [ ] 与现有分类管理功能无冲突
- [ ] 支持主流浏览器（Chrome、Firefox、Safari、Edge）
- [ ] 响应式设计适配移动端

---

**文档状态**: ✅ 已完成  
**下一步**: 技术架构设计和开发任务分解
