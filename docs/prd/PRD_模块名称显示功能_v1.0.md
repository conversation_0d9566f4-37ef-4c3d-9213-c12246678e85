# 模块名称显示功能 PRD

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-03
- **负责人**: Emma (产品经理)
- **项目**: 继续深造-分类管理系统
- **功能模块**: 模块ID转模块名称显示

## 2. 背景与问题陈述

### 2.1 当前问题
- 分类管理页面的"所属模块ID"字段直接显示数字ID，用户体验不佳
- 用户无法直观了解分类所属的具体模块名称
- 需要额外查询才能知道模块ID对应的模块信息

### 2.2 业务价值
- 提升分类管理的用户体验和可读性
- 减少用户的认知负担和操作步骤
- 提高数据展示的直观性和友好性

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
- **主要目标**: 将分类表格中的模块ID显示为模块名称
- **用户体验目标**: 提供直观的模块信息展示
- **技术目标**: 实现高效的异步数据转换机制

### 3.2 关键结果 (Key Results)
- 分类表格中模块字段显示为可读的模块名称
- 模块名称加载响应时间 < 300ms
- 异常情况下有合适的降级显示
- 不影响表格的整体加载性能

### 3.3 反向指标 (Counter Metrics)
- 表格初始加载时间不增加超过100ms
- 不影响现有分类管理功能
- 不增加过多的API请求负担

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 系统管理员
- **次要用户**: 内容管理员
- **使用场景**: 分类管理、内容组织

### 4.2 用户故事
**作为** 系统管理员  
**我希望** 在分类列表中直接看到模块名称而不是模块ID  
**以便于** 快速识别分类所属的模块，提高管理效率

**作为** 内容管理员  
**我希望** 能够直观地了解分类的模块归属  
**以便于** 更好地组织和管理学习内容

## 5. 功能规格详述

### 5.1 核心功能
1. **模块名称显示**
   - 将"所属模块ID"列标题改为"所属模块"
   - 显示模块名称而不是模块ID
   - 支持异步加载模块信息

2. **加载状态处理**
   - 模块名称加载时显示加载指示器
   - 加载完成后显示模块名称
   - 加载失败时显示降级信息

3. **性能优化**
   - 实施模块信息缓存机制
   - 避免重复的API请求
   - 批量加载优化

### 5.2 技术实现规格
1. **API接口扩展**
   - 在 `InzLearningModules.api.ts` 中新增 `queryById` 接口
   - 接口路径: `/inz_learning_modules/inzLearningModules/queryById`
   - 参数: `{ id: moduleId }`

2. **表格列配置**
   - 使用 `customRender` 实现异步数据转换
   - 实现模块信息的缓存和状态管理
   - 添加加载状态和错误处理

3. **数据缓存策略**
   - 使用 Map 结构缓存已加载的模块信息
   - 避免重复请求相同的模块ID
   - 实施合理的缓存过期机制

### 5.3 UI/UX设计
1. **显示状态**
   - 加载中: 显示 `<Spin size="small" />` 加载指示器
   - 加载成功: 显示模块名称
   - 加载失败: 显示 "未知模块" 或原始ID

2. **视觉效果**
   - 模块名称使用正常字体显示
   - 加载失败时使用灰色字体
   - 保持与其他列的视觉一致性

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 分类主表格的模块名称显示
- 子分类表格的模块名称显示（如果包含模块字段）
- 模块信息的异步加载和缓存
- 加载状态和错误处理
- API接口的扩展

### 6.2 排除功能 (Out of Scope)
- 模块信息的实时更新（需要刷新页面）
- 模块名称的点击跳转功能
- 模块信息的批量预加载
- 其他页面的模块名称显示

## 7. 依赖与风险

### 7.1 内部依赖
- `InzLearningModules.api.ts` 的 queryById 接口实现
- BasicTable 组件的 customRender 功能
- 现有分类数据的 moduleId 字段

### 7.2 外部依赖
- 后端模块查询接口的稳定性
- 模块数据的完整性和准确性

### 7.3 潜在风险
- **性能风险**: 大量模块ID导致频繁API调用
- **数据风险**: 模块信息不存在或已删除
- **用户体验风险**: 加载延迟影响表格显示

### 7.4 风险缓解策略
- 实施智能缓存机制减少API调用
- 添加完善的错误处理和降级显示
- 实施加载状态提示提升用户体验

## 8. 技术实现方案

### 8.1 API接口扩展
```typescript
// 在 InzLearningModules.api.ts 中添加
enum Api {
  // 现有接口...
  queryById = '/inz_learning_modules/inzLearningModules/queryById',
}

export const queryModuleById = (id: string) => {
  return defHttp.get({
    url: Api.queryById,
    params: { id },
  });
};
```

### 8.2 表格列配置修改
```typescript
// 在 InzLearningCategorys.data.ts 中修改
{
  title: '所属模块',
  align: 'center',
  dataIndex: 'moduleId',
  width: 150,
  customRender: ({ text: moduleId }) => {
    return h(ModuleNameRenderer, { moduleId });
  },
}
```

### 8.3 模块名称渲染组件
```vue
<template>
  <span v-if="loading">
    <Spin size="small" />
  </span>
  <span v-else-if="moduleName" class="module-name">
    {{ moduleName }}
  </span>
  <span v-else class="module-error">
    未知模块
  </span>
</template>

<script setup>
// 异步加载模块信息的逻辑
</script>
```

## 9. 验收标准

### 9.1 功能验收
- [ ] 分类表格显示模块名称而不是模块ID
- [ ] 模块名称正确对应模块ID
- [ ] 加载状态正确显示
- [ ] 错误情况有合适的降级显示
- [ ] 缓存机制正常工作

### 9.2 性能验收
- [ ] 模块名称加载响应时间 < 300ms
- [ ] 表格初始加载时间增加 < 100ms
- [ ] 相同模块ID不重复请求
- [ ] 内存使用合理

### 9.3 用户体验验收
- [ ] 加载过程用户体验流畅
- [ ] 错误状态用户友好
- [ ] 视觉效果与整体风格一致
- [ ] 支持主流浏览器

## 10. 发布计划

### 10.1 开发阶段
1. **Phase 1**: API接口扩展 (0.5天)
2. **Phase 2**: 模块名称渲染组件开发 (0.5天)
3. **Phase 3**: 表格集成和测试 (0.5天)

### 10.2 测试计划
- 单元测试: 模块名称渲染逻辑测试
- 集成测试: API调用和缓存机制测试
- 性能测试: 大量数据场景验证

### 10.3 上线策略
- 灰度发布: 先在测试环境验证
- 全量发布: 确认无问题后全量上线
- 监控指标: API响应时间、错误率、用户体验指标

---

**文档状态**: ✅ 已完成  
**下一步**: 技术架构设计和开发任务分解
