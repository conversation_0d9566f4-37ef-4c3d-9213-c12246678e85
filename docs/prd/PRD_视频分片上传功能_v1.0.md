# 视频分片上传功能 - 产品需求文档 (PRD)

## 1. 文档信息
- **文档版本**: v1.0
- **创建时间**: 2025-08-03
- **负责人**: Emma (产品经理)
- **审核人**: Mike (团队领袖)
- **项目状态**: 待开发

## 2. 版本历史
| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-08-03 | 初始版本，定义视频分片上传需求 | Emma |

## 3. 背景与问题陈述

### 3.1 当前问题
在继续深造-视频管理模块中，视频文件上传存在以下问题：
1. **文件大小限制**: 当前限制500MB，无法满足高清长视频需求
2. **上传体验差**: 大文件上传时间长，无进度显示，网络中断需重新上传
3. **系统压力大**: 大文件上传占用大量内存和带宽资源
4. **失败率高**: 网络不稳定时大文件上传容易失败

### 3.2 业务价值
- 支持更大的视频文件上传（GB级别）
- 提升用户上传体验和成功率
- 减少系统资源占用
- 增强系统稳定性和可靠性

## 4. 目标与成功指标

### 4.1 项目目标 (Objectives)
1. **大文件支持**: 支持GB级视频文件上传
2. **用户体验优化**: 提供进度显示、断点续传、暂停恢复功能
3. **系统性能优化**: 降低内存占用，提高上传成功率
4. **向后兼容**: 保持现有小文件上传功能不变

### 4.2 关键结果 (Key Results)
- 支持最大5GB视频文件上传
- 上传成功率提升至95%以上
- 内存占用降低90%以上
- 支持断点续传和进度显示

### 4.3 反向指标 (Counter Metrics)
- 小文件上传性能不下降
- 系统复杂度控制在合理范围
- 存储成本增加不超过20%

## 5. 用户画像与用户故事

### 5.1 目标用户
- **主要用户**: 教学内容创作者、教育机构管理员
- **次要用户**: 系统管理员

### 5.2 用户故事
**作为** 教学内容创作者  
**我希望** 能够上传高清长视频（1-2GB）  
**以便于** 提供更好的教学内容质量

**作为** 教育机构管理员  
**我希望** 上传过程中能看到进度并支持暂停恢复  
**以便于** 在网络不稳定时也能成功上传视频

## 6. 功能规格详述

### 6.1 核心功能

#### 6.1.1 智能上传策略
- **小文件**: ≤50MB使用普通上传
- **大文件**: >50MB自动切换分片上传
- **分片大小**: 默认5MB，可配置
- **并发控制**: 最多3个分片并发上传

#### 6.1.2 分片上传流程
1. **文件检测**: 计算文件MD5，检查是否支持秒传
2. **任务初始化**: 创建上传任务，分配任务ID
3. **文件分片**: 前端将文件切分为固定大小的分片
4. **并发上传**: 多个分片并发上传，实时更新进度
5. **分片合并**: 所有分片上传完成后，后端合并为完整文件
6. **清理临时**: 删除临时分片文件，返回最终URL

#### 6.1.3 用户交互功能
- **进度显示**: 实时显示上传进度百分比和速度
- **暂停/恢复**: 支持暂停上传并稍后恢复
- **取消上传**: 支持取消上传并清理临时文件
- **断点续传**: 网络中断后自动或手动恢复上传
- **错误重试**: 失败分片自动重试，最多3次

#### 6.1.4 高级功能
- **秒传检测**: 通过MD5检查，相同文件直接返回URL
- **格式验证**: 严格验证视频文件格式和编码
- **预览生成**: 上传完成后自动生成视频缩略图
- **压缩建议**: 检测到超大文件时提供压缩建议

### 6.2 技术实现规格

#### 6.2.1 前端组件设计
```vue
<template>
  <div class="chunk-video-upload">
    <!-- 文件选择区域 -->
    <div class="upload-area" @drop="handleDrop" @dragover.prevent>
      <div v-if="!uploading" class="upload-trigger">
        <Icon icon="ant-design:cloud-upload-outlined" size="48" />
        <p>点击或拖拽视频文件到此区域上传</p>
        <p class="upload-hint">支持mp4、avi、mov等格式，最大5GB</p>
      </div>
      
      <!-- 上传进度区域 -->
      <div v-else class="upload-progress">
        <div class="file-info">
          <Icon icon="ant-design:video-camera-outlined" />
          <span>{{ fileName }}</span>
          <span class="file-size">{{ formatFileSize(fileSize) }}</span>
        </div>
        
        <Progress 
          :percent="uploadProgress" 
          :status="uploadStatus"
          :show-info="true"
        />
        
        <div class="upload-stats">
          <span>上传速度: {{ uploadSpeed }}</span>
          <span>剩余时间: {{ remainingTime }}</span>
        </div>
        
        <div class="upload-controls">
          <Button @click="pauseUpload" v-if="!paused">暂停</Button>
          <Button @click="resumeUpload" v-if="paused">恢复</Button>
          <Button @click="cancelUpload" danger>取消</Button>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 6.2.2 后端API设计
```java
@RestController
@RequestMapping("/sys/video-upload")
public class VideoChunkUploadController {
    
    @PostMapping("/init")
    public Result<UploadTask> initUpload(@RequestBody InitUploadRequest request);
    
    @PostMapping("/chunk")
    public Result<String> uploadChunk(@RequestParam MultipartFile file, 
                                     @RequestParam String taskId,
                                     @RequestParam Integer chunkIndex);
    
    @PostMapping("/merge")
    public Result<String> mergeChunks(@RequestParam String taskId);
    
    @GetMapping("/progress/{taskId}")
    public Result<UploadProgress> getProgress(@PathVariable String taskId);
    
    @PostMapping("/cancel")
    public Result<Void> cancelUpload(@RequestParam String taskId);
}
```

#### 6.2.3 数据库设计
```sql
-- 视频上传任务表
CREATE TABLE video_upload_task (
  id varchar(32) PRIMARY KEY,
  file_name varchar(255) NOT NULL,
  file_size bigint NOT NULL,
  file_hash varchar(64),
  chunk_size int DEFAULT 5242880,
  total_chunks int NOT NULL,
  uploaded_chunks int DEFAULT 0,
  status varchar(20) DEFAULT 'UPLOADING',
  final_url varchar(500),
  video_format varchar(20),
  duration int,
  resolution varchar(20),
  created_by varchar(50),
  created_time datetime DEFAULT CURRENT_TIMESTAMP,
  updated_time datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 视频分片详情表
CREATE TABLE video_upload_chunk (
  id varchar(32) PRIMARY KEY,
  task_id varchar(32) NOT NULL,
  chunk_index int NOT NULL,
  chunk_size int NOT NULL,
  chunk_hash varchar(64),
  storage_path varchar(500),
  status varchar(20) DEFAULT 'PENDING',
  retry_count int DEFAULT 0,
  created_time datetime DEFAULT CURRENT_TIMESTAMP
);
```

### 6.3 UI/UX设计

#### 6.3.1 上传界面设计
- **拖拽上传**: 支持拖拽文件到上传区域
- **进度可视化**: 圆形进度条 + 百分比 + 上传速度
- **状态指示**: 不同颜色表示上传状态（进行中/暂停/错误/完成）
- **操作按钮**: 暂停/恢复/取消按钮，状态相关显示

#### 6.3.2 交互流程
1. **文件选择**: 点击或拖拽选择视频文件
2. **格式检查**: 自动检查文件格式，不支持时提示
3. **大小检查**: 超过5GB时提示文件过大
4. **上传确认**: 显示文件信息，用户确认开始上传
5. **进度跟踪**: 实时显示上传进度和统计信息
6. **完成处理**: 上传完成后显示成功信息和预览

## 7. 范围定义

### 7.1 包含功能 (In Scope)
- 视频文件的分片上传功能
- 上传进度显示和控制
- 断点续传和错误重试
- 秒传检测和去重
- 视频格式验证
- 临时文件管理和清理

### 7.2 排除功能 (Out of Scope)
- 视频转码和压缩功能
- 视频内容审核
- 批量上传功能
- 其他文件类型的分片上传
- 视频播放和预览功能

## 8. 依赖与风险

### 8.1 内部依赖
- 分片上传基础框架
- 文件存储服务（本地/MinIO/OSS）
- 数据库支持
- 前端文件处理API

### 8.2 外部依赖
- 浏览器File API支持
- 网络稳定性
- 存储空间充足

### 8.3 潜在风险
- **存储成本**: 临时分片文件增加存储成本
- **网络要求**: 对网络稳定性要求较高
- **浏览器兼容**: 老版本浏览器可能不支持
- **并发限制**: 大量用户同时上传可能影响性能

### 8.4 风险缓解策略
- 实施自动清理机制减少存储成本
- 提供降级方案支持普通上传
- 添加浏览器兼容性检查
- 实施上传队列和限流机制

## 9. 发布初步计划

### 9.1 开发阶段 (2周)
- **Week 1**: 后端分片上传API开发
- **Week 2**: 前端组件开发和集成

### 9.2 测试阶段 (1周)
- 功能测试和性能测试
- 兼容性测试
- 用户体验测试

### 9.3 上线计划
- 灰度发布: 部分用户先体验
- 全量发布: 确认无问题后全量上线
- 监控优化: 持续监控和优化

## 10. 成功标准

### 10.1 功能标准
- [ ] 支持5GB视频文件上传
- [ ] 上传进度实时显示
- [ ] 断点续传功能正常
- [ ] 秒传检测工作正常
- [ ] 错误处理完善

### 10.2 性能标准
- [ ] 上传成功率 ≥ 95%
- [ ] 内存占用 ≤ 50MB
- [ ] 上传速度接近网络带宽上限
- [ ] 页面响应时间 ≤ 2秒

### 10.3 用户体验标准
- [ ] 操作流程直观易懂
- [ ] 错误提示清晰有用
- [ ] 支持主流浏览器
- [ ] 移动端基本可用
