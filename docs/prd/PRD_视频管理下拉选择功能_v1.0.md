# 视频管理下拉选择功能 - 产品需求文档 (PRD)

## 1. 文档信息
- **文档版本**: v1.0
- **创建时间**: 2025-08-03
- **负责人**: Emma (产品经理)
- **审核人**: Mike (团队领袖)
- **项目状态**: 待开发

## 2. 版本历史
| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| v1.0 | 2025-08-03 | 初始版本，定义视频管理下拉选择需求 | Emma |

## 3. 背景与问题陈述

### 3.1 当前问题
在视频管理模块的新增/编辑功能中，用户需要手动输入模块ID和分类ID，存在以下问题：
1. **用户体验差**: 用户需要记住或查找具体的ID值，操作繁琐
2. **错误率高**: 手动输入ID容易出错，导致数据关联错误
3. **效率低下**: 需要在多个页面间切换查找ID信息
4. **可读性差**: ID数字对用户没有直观意义

### 3.2 业务价值
- 提升视频内容管理效率
- 减少数据录入错误
- 改善用户操作体验
- 提高系统易用性

## 4. 目标与成功指标

### 4.1 项目目标 (Objectives)
1. **用户体验优化**: 将模块ID和分类ID输入框改为下拉选择
2. **数据准确性提升**: 通过下拉选择避免手动输入错误
3. **操作效率提升**: 用户可直接看到模块名称和分类名称进行选择

### 4.2 关键结果 (Key Results)
- 视频新增/编辑表单中模块和分类字段支持下拉选择
- 下拉选项显示名称，选择后提交对应ID
- 支持搜索和筛选功能
- 保持现有数据结构和API兼容性

### 4.3 反向指标 (Counter Metrics)
- 页面加载时间不超过现有时间的20%
- 不影响现有视频数据的完整性
- 不破坏现有的API接口

## 5. 用户画像与用户故事

### 5.1 目标用户
- **主要用户**: 内容管理员、教学管理员
- **次要用户**: 系统管理员

### 5.2 用户故事
**作为** 内容管理员  
**我希望** 在新增视频时通过下拉选择模块和分类  
**以便于** 快速准确地设置视频的归属关系

**作为** 教学管理员  
**我希望** 能够看到模块名称和分类名称而不是ID  
**以便于** 更直观地理解和管理视频内容

## 6. 功能规格详述

### 6.1 核心功能

#### 6.1.1 模块选择下拉框
- **显示内容**: 模块名称 (如: "幼儿启蒙字级单词篇")
- **提交数据**: 模块ID (如: "1001")
- **数据来源**: `/inz_learning_modules/inzLearningModules/list` API
- **支持功能**: 搜索、筛选、分页加载

#### 6.1.2 分类选择下拉框
- **显示内容**: 分类名称 (如: "基础词汇")
- **提交数据**: 分类ID (如: "2001")
- **数据来源**: `/inz_learning_categorys/inzLearningCategorys/list` API
- **支持功能**: 搜索、筛选、级联选择

#### 6.1.3 级联选择功能
- 选择模块后，分类下拉框自动筛选该模块下的分类
- 支持清空模块选择，分类恢复显示所有选项
- 智能缓存机制，避免重复API调用

### 6.2 技术实现规格

#### 6.2.1 表单组件升级
- 将 `moduleId` 字段组件从 `Input` 改为 `ApiSelect`
- 将 `categoryId` 字段组件从 `Input` 改为 `ApiSelect`
- 配置相应的API接口和数据映射

#### 6.2.2 API接口配置
```typescript
// 模块下拉选择配置
{
  label: '所属模块',
  field: 'moduleId',
  component: 'ApiSelect',
  componentProps: {
    api: getModuleList,
    labelField: 'moduleName',
    valueField: 'id',
    placeholder: '请选择模块',
    showSearch: true,
  },
}

// 分类下拉选择配置
{
  label: '所属分类',
  field: 'categoryId',
  component: 'ApiSelect',
  componentProps: {
    api: getCategoryList,
    labelField: 'categoryName',
    valueField: 'id',
    placeholder: '请选择分类',
    showSearch: true,
  },
}
```

#### 6.2.3 数据缓存策略
- 模块列表数据缓存，避免重复请求
- 分类列表按模块ID缓存
- 实施合理的缓存过期机制

### 6.3 UI/UX设计

#### 6.3.1 下拉框样式
- 使用Ant Design的Select组件
- 支持搜索功能，输入关键词筛选
- 显示加载状态和空数据提示
- 保持与现有表单风格一致

#### 6.3.2 交互设计
- 模块选择后自动触发分类列表更新
- 支持键盘导航和快捷选择
- 提供清空选择功能
- 错误状态友好提示

## 7. 范围定义

### 7.1 包含功能 (In Scope)
- 视频新增表单的模块和分类下拉选择
- 视频编辑表单的模块和分类下拉选择
- 模块和分类数据的异步加载
- 搜索和筛选功能
- 级联选择功能
- 数据缓存机制

### 7.2 排除功能 (Out of Scope)
- 其他模块的下拉选择改造
- 模块和分类的新增/编辑功能
- 批量导入时的下拉选择
- 高级查询中的下拉选择

## 8. 依赖与风险

### 8.1 内部依赖
- 模块列表API (`/inz_learning_modules/inzLearningModules/list`)
- 分类列表API (`/inz_learning_categorys/inzLearningCategorys/list`)
- ApiSelect组件的正常功能
- 现有表单验证机制

### 8.2 外部依赖
- 后端模块和分类接口的稳定性
- 数据库中模块和分类数据的完整性

### 8.3 潜在风险
- **性能风险**: 大量数据时下拉框加载缓慢
- **数据风险**: 模块或分类数据不存在导致选择失败
- **兼容性风险**: 现有数据格式与新组件不兼容

### 8.4 风险缓解策略
- 实施分页加载和虚拟滚动
- 添加完善的错误处理和降级显示
- 保持数据格式向后兼容
- 实施渐进式升级策略

## 9. 发布初步计划

### 9.1 开发阶段
- **阶段1**: API接口准备和测试 (1天)
- **阶段2**: 表单组件改造 (2天)
- **阶段3**: 级联选择功能开发 (1天)
- **阶段4**: 测试和优化 (1天)

### 9.2 测试计划
- 功能测试: 验证下拉选择和数据提交
- 性能测试: 验证大数据量下的加载性能
- 兼容性测试: 验证现有数据的兼容性
- 用户体验测试: 验证操作流程的流畅性

### 9.3 上线计划
- 灰度发布: 先在测试环境验证
- 全量发布: 确认无问题后全量上线
- 数据监控: 监控新功能的使用情况和错误率
