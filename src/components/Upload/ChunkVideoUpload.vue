<template>
  <div class="chunk-video-upload">
    <!-- 文件选择区域 -->
    <div v-if="!uploading" class="upload-area">
      <a-upload-dragger 
        :show-upload-list="false" 
        :before-upload="beforeUpload"
        :accept="accept"
        :multiple="false"
      >
        <p class="ant-upload-drag-icon">
          <Icon icon="ant-design:video-camera-outlined" :size="48" />
        </p>
        <p class="ant-upload-text">点击或拖拽视频文件到此区域上传</p>
        <p class="ant-upload-hint">{{ uploadHint }}</p>
      </a-upload-dragger>
    </div>
    
    <!-- 上传进度区域 -->
    <div v-else class="upload-progress-container">
      <div class="file-info">
        <Icon icon="ant-design:video-camera-outlined" :size="20" />
        <span class="file-name">{{ fileName }}</span>
        <span class="file-size">{{ formatFileSize(fileSize) }}</span>
      </div>
      
      <a-progress 
        :percent="Math.round(uploadProgress)" 
        :status="progressStatus"
        :stroke-color="progressColor"
        :show-info="true"
      />
      
      <div class="upload-stats">
        <span>上传速度: {{ uploadSpeed }}</span>
        <span>已上传: {{ formatFileSize(uploadedSize) }}</span>
        <span>剩余时间: {{ remainingTime }}</span>
      </div>
      
      <div class="upload-controls">
        <a-button @click="pauseUpload" v-if="!paused && uploading" size="small">
          <Icon icon="ant-design:pause-outlined" />
          暂停
        </a-button>
        <a-button @click="resumeUpload" v-if="paused" type="primary" size="small">
          <Icon icon="ant-design:play-circle-outlined" />
          恢复
        </a-button>
        <a-button @click="cancelUpload" danger size="small">
          <Icon icon="ant-design:close-outlined" />
          取消
        </a-button>
      </div>
    </div>
    
    <!-- 上传成功显示 -->
    <div v-if="uploadSuccess" class="upload-success">
      <Icon icon="ant-design:check-circle-outlined" style="color: #52c41a; font-size: 20px;" />
      <span>{{ fileName }} 上传成功</span>
      <a-button @click="resetUpload" size="small" type="link">重新上传</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';

// 组件属性
interface Props {
  value?: string;
  maxSize?: number; // MB
  accept?: string;
  chunkSize?: number; // MB
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  maxSize: 5120, // 5GB
  accept: '.mp4,.avi,.mov,.wmv,.flv,.webm',
  chunkSize: 5, // 5MB
  disabled: false,
});

// 事件
const emit = defineEmits(['update:value', 'success', 'error', 'progress']);

// 响应式数据
const uploading = ref(false);
const paused = ref(false);
const uploadSuccess = ref(false);
const fileName = ref('');
const fileSize = ref(0);
const uploadProgress = ref(0);
const uploadSpeed = ref('0 KB/s');
const uploadedSize = ref(0);
const remainingTime = ref('计算中...');
const currentFile = ref<File | null>(null);

// 上传任务相关
const taskId = ref('');
const totalChunks = ref(0);
const uploadedChunks = ref(0);

// 计算属性
const progressStatus = computed(() => {
  if (paused.value) return 'normal';
  if (uploadProgress.value === 100) return 'success';
  return 'active';
});

const progressColor = computed(() => {
  if (paused.value) return '#faad14';
  if (uploadProgress.value === 100) return '#52c41a';
  return '#1890ff';
});

const uploadHint = computed(() => {
  return `支持${props.accept.replace(/\./g, '').toUpperCase()}等格式，最大${formatFileSize(props.maxSize * 1024 * 1024)}`;
});

// 监听value变化
watch(() => props.value, (newValue) => {
  if (newValue && !uploading.value) {
    uploadSuccess.value = true;
  }
});

// 方法
const beforeUpload = (file: File) => {
  if (props.disabled) {
    message.warning('当前状态不允许上传');
    return false;
  }

  // 验证文件格式
  if (!isValidVideoFile(file)) {
    message.error('请选择有效的视频文件');
    return false;
  }
  
  // 验证文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    message.error(`文件大小不能超过${formatFileSize(props.maxSize * 1024 * 1024)}`);
    return false;
  }
  
  currentFile.value = file;
  startUpload(file);
  return false; // 阻止默认上传
};

const startUpload = async (file: File) => {
  try {
    uploading.value = true;
    uploadSuccess.value = false;
    fileName.value = file.name;
    fileSize.value = file.size;
    uploadProgress.value = 0;
    uploadedSize.value = 0;
    
    // 检查是否为大文件，决定使用分片上传还是普通上传
    const threshold = 50 * 1024 * 1024; // 50MB
    
    if (file.size > threshold) {
      await startChunkUpload(file);
    } else {
      await startNormalUpload(file);
    }
  } catch (error) {
    console.error('上传失败:', error);
    message.error(`上传失败: ${error.message || '未知错误'}`);
    uploading.value = false;
    emit('error', error);
  }
};

const startChunkUpload = async (file: File) => {
  // 计算分片数量
  const chunkSizeBytes = props.chunkSize * 1024 * 1024;
  totalChunks.value = Math.ceil(file.size / chunkSizeBytes);
  uploadedChunks.value = 0;
  
  // 计算文件MD5（简化版，实际应该使用Web Worker）
  const fileHash = await calculateSimpleHash(file);
  
  // 初始化上传任务
  const initResponse = await fetch('/jeecg-boot/sys/chunk-upload/init', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Access-Token': getToken(),
    },
    body: JSON.stringify({
      fileName: file.name,
      fileSize: file.size,
      fileHash: fileHash,
      chunkSize: chunkSizeBytes,
      bizPath: 'video',
      uploadType: 'local'
    })
  });
  
  const initResult = await initResponse.json();
  if (!initResult.success) {
    throw new Error(initResult.message || '初始化上传任务失败');
  }
  
  taskId.value = initResult.result.id;
  
  // 检查是否支持秒传
  if (initResult.result.status === 'COMPLETED') {
    uploadProgress.value = 100;
    uploadSuccess.value = true;
    uploading.value = false;
    emit('update:value', initResult.result.finalUrl);
    emit('success', initResult.result.finalUrl);
    message.success('文件已存在，秒传成功！');
    return;
  }
  
  // 开始分片上传
  await uploadChunks(file, chunkSizeBytes);
};

const uploadChunks = async (file: File, chunkSize: number) => {
  const startTime = Date.now();
  
  for (let i = 0; i < totalChunks.value; i++) {
    if (paused.value) {
      // 暂停状态，等待恢复
      await waitForResume();
    }
    
    const start = i * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    const chunk = file.slice(start, end);
    
    // 上传分片
    await uploadSingleChunk(chunk, i);
    
    uploadedChunks.value = i + 1;
    uploadedSize.value = Math.min(end, file.size);
    uploadProgress.value = (uploadedSize.value / file.size) * 100;
    
    // 计算上传速度和剩余时间
    const elapsed = (Date.now() - startTime) / 1000;
    const speed = uploadedSize.value / elapsed;
    uploadSpeed.value = formatSpeed(speed);
    
    const remaining = (file.size - uploadedSize.value) / speed;
    remainingTime.value = formatTime(remaining);
    
    emit('progress', {
      percentage: uploadProgress.value,
      uploadedSize: uploadedSize.value,
      totalSize: file.size,
      speed: speed
    });
  }
  
  // 合并分片
  await mergeChunks();
};

const uploadSingleChunk = async (chunk: Blob, index: number) => {
  const formData = new FormData();
  formData.append('file', chunk);
  formData.append('taskId', taskId.value);
  formData.append('chunkIndex', index.toString());
  
  const response = await fetch('/jeecg-boot/sys/chunk-upload/upload-chunk', {
    method: 'POST',
    headers: {
      'X-Access-Token': getToken(),
    },
    body: formData
  });
  
  const result = await response.json();
  if (!result.success) {
    throw new Error(`分片${index}上传失败: ${result.message}`);
  }
};

const mergeChunks = async () => {
  const response = await fetch('/jeecg-boot/sys/chunk-upload/merge', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'X-Access-Token': getToken(),
    },
    body: `taskId=${taskId.value}`
  });
  
  const result = await response.json();
  if (!result.success) {
    throw new Error(`合并文件失败: ${result.message}`);
  }
  
  uploadProgress.value = 100;
  uploadSuccess.value = true;
  uploading.value = false;
  
  emit('update:value', result.result);
  emit('success', result.result);
  message.success('视频上传成功！');
};

const startNormalUpload = async (file: File) => {
  // 普通上传逻辑（小文件）
  const formData = new FormData();
  formData.append('file', file);
  formData.append('biz', 'video');
  
  const xhr = new XMLHttpRequest();
  
  // 上传进度
  xhr.upload.addEventListener('progress', (e) => {
    if (e.lengthComputable) {
      uploadProgress.value = (e.loaded / e.total) * 100;
      uploadedSize.value = e.loaded;
      
      const speed = e.loaded / ((Date.now() - startTime) / 1000);
      uploadSpeed.value = formatSpeed(speed);
      
      const remaining = (e.total - e.loaded) / speed;
      remainingTime.value = formatTime(remaining);
      
      emit('progress', {
        percentage: uploadProgress.value,
        uploadedSize: e.loaded,
        totalSize: e.total,
        speed: speed
      });
    }
  });
  
  const startTime = Date.now();
  
  return new Promise((resolve, reject) => {
    xhr.onload = () => {
      if (xhr.status === 200) {
        const result = JSON.parse(xhr.responseText);
        if (result.success) {
          uploadProgress.value = 100;
          uploadSuccess.value = true;
          uploading.value = false;
          
          emit('update:value', result.message);
          emit('success', result.message);
          message.success('视频上传成功！');
          resolve(result.message);
        } else {
          reject(new Error(result.message || '上传失败'));
        }
      } else {
        reject(new Error('网络错误'));
      }
    };
    
    xhr.onerror = () => reject(new Error('网络错误'));
    
    xhr.open('POST', '/jeecg-boot/sys/common/upload');
    xhr.setRequestHeader('X-Access-Token', getToken());
    xhr.send(formData);
  });
};

const pauseUpload = () => {
  paused.value = true;
  message.info('上传已暂停');
};

const resumeUpload = () => {
  paused.value = false;
  message.info('上传已恢复');
};

const cancelUpload = () => {
  uploading.value = false;
  paused.value = false;
  uploadProgress.value = 0;
  uploadedSize.value = 0;
  
  // 如果有任务ID，调用取消接口
  if (taskId.value) {
    fetch('/jeecg-boot/sys/chunk-upload/cancel', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-Access-Token': getToken(),
      },
      body: `taskId=${taskId.value}`
    }).catch(console.error);
  }
  
  message.info('上传已取消');
};

const resetUpload = () => {
  uploadSuccess.value = false;
  uploading.value = false;
  paused.value = false;
  uploadProgress.value = 0;
  uploadedSize.value = 0;
  fileName.value = '';
  fileSize.value = 0;
  taskId.value = '';
  currentFile.value = null;
  
  emit('update:value', '');
};

const waitForResume = (): Promise<void> => {
  return new Promise((resolve) => {
    const checkResume = () => {
      if (!paused.value) {
        resolve();
      } else {
        setTimeout(checkResume, 100);
      }
    };
    checkResume();
  });
};

// 工具函数
const isValidVideoFile = (file: File): boolean => {
  const validTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const acceptedExtensions = props.accept.split(',').map(ext => ext.trim().replace('.', ''));
  
  return validTypes.includes(file.type) || acceptedExtensions.includes(fileExtension || '');
};

const calculateSimpleHash = async (file: File): Promise<string> => {
  // 简化的哈希计算，实际项目中应该使用更完整的MD5计算
  const firstChunk = file.slice(0, 1024);
  const lastChunk = file.slice(-1024);
  const middleChunk = file.slice(Math.floor(file.size / 2), Math.floor(file.size / 2) + 1024);
  
  const combined = await new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.readAsDataURL(new Blob([firstChunk, middleChunk, lastChunk]));
  });
  
  return btoa(combined).substring(0, 32);
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const formatSpeed = (bytesPerSecond: number): string => {
  return formatFileSize(bytesPerSecond) + '/s';
};

const formatTime = (seconds: number): string => {
  if (!isFinite(seconds) || seconds <= 0) return '计算中...';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

const getToken = (): string => {
  return localStorage.getItem('Access-Token') || '';
};
</script>

<style scoped>
.chunk-video-upload {
  width: 100%;
}

.upload-area {
  margin-bottom: 16px;
}

.upload-progress-container {
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.file-name {
  font-weight: 500;
  color: #262626;
}

.file-size {
  color: #8c8c8c;
  font-size: 12px;
}

.upload-stats {
  display: flex;
  justify-content: space-between;
  margin: 12px 0;
  font-size: 12px;
  color: #8c8c8c;
}

.upload-controls {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.upload-success {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  color: #52c41a;
}
</style>
