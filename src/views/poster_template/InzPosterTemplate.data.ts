import { BasicColumn } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '海报标题',
    align: 'center',
    dataIndex: 'title',
  },
  {
    title: '背景图',
    align: 'center',
    dataIndex: 'backgroundImage',
    customRender: render.renderImage,
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: '1' },
        { text: '否', value: '0' },
      ]);
    },
  },
  {
    title: '距左边',
    align: 'center',
    dataIndex: 'qrCodePositionX',
  },
  {
    title: '距顶部',
    align: 'center',
    dataIndex: 'qrCodePositionY',
  },
  {
    title: '二维码宽度',
    align: 'center',
    dataIndex: 'qrCodeSize',
  },
];

// 高级查询数据
export const superQuerySchema = {
  title: { title: '海报标题', order: 0, view: 'text', type: 'string' },
  backgroundImage: { title: '背景图', order: 1, view: 'image', type: 'string' },
  status: { title: '状态', order: 2, view: 'number', type: 'number' },
  qrCodeStartWidth: { title: '二维码开始宽度', order: 3, view: 'number', type: 'number' },
  qrCodeEndHeigh: { title: '二维码结束高度', order: 4, view: 'number', type: 'number' },
};
