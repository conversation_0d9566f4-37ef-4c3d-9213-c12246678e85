<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :expandedRowKeys="expandedRowKeys" @expand="handleExpand">
      <!-- 内嵌table区域 begin -->
      <template #expandedRowRender="{ record }">
        <a-tabs tabPosition="top">
          <a-tab-pane tab="词书课节管理" key="inzWordBookChapter" forceRender>
            <inzWordBookChapterSubTable :id="expandedRowKeys[0]" />
          </a-tab-pane>
        </a-tabs>
      </template>
      <!-- 内嵌table区域 end -->
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'books:inz_word_books:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增 </a-button>
        <a-button type="primary" v-auth="'books:inz_word_books:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出 </a-button>
        <j-upload-button type="primary" v-auth="'books:inz_word_books:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入
        </j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'books:inz_word_books:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }"></template>
    </BasicTable>
    <!-- 表单区域 -->
    <InzWordBooksModal @register="registerModal" @success="handleSuccess"></InzWordBooksModal>
  </div>
</template>

<script lang="ts" name="books-inzWordBooks" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import InzWordBooksModal from './components/InzWordBooksModal.vue';
  import InzWordBookChapterSubTable from './subTables/InzWordBookChapterSubTable.vue';
  import { columns, searchFormSchema, superQuerySchema } from './InzWordBooks.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list, savePrompt } from './InzWordBooks.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';

  const { createMessage } = useMessage();
  const queryParam = reactive<any>({});
  // 展开key
  const expandedRowKeys = ref<any[]>([]);
  //注册model
  const [registerModal, { openModal }] = useModal();
  const userStore = useUserStore();

  // 提示词相关状态
  const promptModalVisible = ref(false);
  const promptModalLoading = ref(false);
  const promptText = ref('');

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '词书表',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '词书表',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 展开事件
   * */
  function handleExpand(expanded, record) {
    expandedRowKeys.value = [];
    if (expanded === true) {
      expandedRowKeys.value.push(record.id);
    }
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'books:inz_word_books:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'books:inz_word_books:delete',
      },
    ];
  }

  /**
   * 打开保存提示词对话框
   */
  function handleSavePrompt() {
    promptModalVisible.value = true;
    promptText.value = '';
  }

  /**
   * 提交保存提示词
   */
  async function handlePromptSubmit() {
    // 校验输入
    if (!promptText.value || promptText.value.trim() === '') {
      createMessage.warning('请输入提示词内容');
      return;
    }

    try {
      promptModalLoading.value = true;

      // 构造参数并调用API
      const result = await savePrompt({
        prompt: promptText.value,
      });

      if (result && result.success) {
        if (result.result && result.result.hash) {
          createMessage.success(`保存提示词成功，获取的Hash值为: ${result.result.hash}`);
        } else {
          createMessage.success('保存提示词成功');
        }
        promptModalVisible.value = false;
      } else {
        createMessage.error(result.message || '保存提示词失败');
      }
    } catch (error) {
      console.error('保存提示词失败:', error);
      createMessage.error('保存提示词失败');
    } finally {
      promptModalLoading.value = false;
    }
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }

  // 提示词对话框样式
  .prompt-modal {
    :deep(.ant-modal-content) {
      border-radius: 8px;
      overflow: hidden;
    }

    :deep(.ant-modal-header) {
      background-color: #f8fafc;
      padding: 16px 24px;
      border-bottom: 1px solid #eaecef;

      .ant-modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }

  .prompt-container {
    padding: 8px 0;
  }

  .prompt-header {
    margin-bottom: 16px;
  }

  .prompt-form {
    margin-top: 16px;

    :deep(.ant-form-item-label) {
      label {
        font-weight: 500;
        color: #374151;
      }
    }
  }

  .prompt-textarea {
    border-radius: 6px;
    resize: vertical;
    font-size: 14px;
    border-color: #d1d5db;

    &:hover,
    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }

  .prompt-footer {
    text-align: right;
    padding: 10px 0;

    .ant-btn {
      margin-left: 8px;
      min-width: 88px;
      border-radius: 4px;

      &-primary {
        background-color: #1890ff;
        border-color: #1890ff;

        &:hover {
          background-color: #40a9ff;
          border-color: #40a9ff;
        }
      }
    }
  }
</style>
