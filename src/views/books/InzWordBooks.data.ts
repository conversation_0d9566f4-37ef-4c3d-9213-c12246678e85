import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { JVxeColumn, JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '词书名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '适用年级',
    align: 'center',
    dataIndex: 'gradeLevel',
  },
  {
    title: '缩略图',
    align: 'center',
    dataIndex: 'thumb',
    customRender: render.renderImage,
  },
  {
    title: '教育层次名称',
    align: 'center',
    dataIndex: 'educationName',
  },
  {
    title: '单词总数',
    align: 'center',
    dataIndex: 'wordsCount',
  },
  {
    title: '排序',
    align: 'center',
    dataIndex: 'sort',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '词书名称',
    field: 'name',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '教育层次名称',
    field: 'educationName',
    component: 'Input',
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '词书名称',
    field: 'name',
    component: 'Input',
    required: true,
  },
  {
    label: '适用年级',
    field: 'gradeLevel',
    component: 'Input',
  },
  {
    label: '缩略图',
    field: 'thumb',
    component: 'JImageUpload',
    componentProps: {
      fileMax: 0,
    },
  },
  {
    label: '教育层次名称',
    field: 'educationId',
    component: 'JTreeSelect',
    required: true,
    componentProps: {
      dict: 'inz_education,name,id',
      pidField: 'pid',
      pidValue: '0',
      hasChildField: 'has_child',
    },
  },
  {
    label: '单词总数',
    field: 'wordsCount',
    component: 'InputNumber',
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
  {
    label: 'Ai生成试题提示词',
    field: 'prompt',
    component: 'InputTextArea',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表列表数据
export const inzWordBookChapterColumns: BasicColumn[] = [
  {
    title: '课节名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '排序',
    align: 'center',
    dataIndex: 'sort',
  },
  {
    title: 'Ai生成试题提示词',
    align: 'center',
    dataIndex: 'prompt',
  },
];
//子表表格配置
export const inzWordBookChapterJVxeColumns: JVxeColumn[] = [
  {
    title: '课节名称',
    key: 'name',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '排序',
    key: 'sort',
    type: JVxeTypes.inputNumber,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: 'Ai生成试题提示词',
    key: 'prompt',
    type: JVxeTypes.textarea,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '词书名称', order: 0, view: 'text', type: 'string' },
  gradeLevel: { title: '适用年级', order: 1, view: 'text', type: 'string' },
  thumb: { title: '缩略图', order: 2, view: 'image', type: 'string' },
  educationName: { title: '教育层次名称', order: 3, view: 'text', type: 'string' },
  wordsCount: { title: '单词总数', order: 4, view: 'number', type: 'number' },
  sort: { title: '排序', order: 5, view: 'number', type: 'number' },
  //子表高级查询
  inzWordBookChapter: {
    title: '词书课节管理',
    view: 'table',
    fields: {
      bookId: { title: '所属词书', order: 0, view: 'text', type: 'string' },
      name: { title: '课节名称', order: 1, view: 'text', type: 'string' },
      sort: { title: '排序', order: 2, view: 'number', type: 'number' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
