import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/books/inzWordBooks/list',
  save='/books/inzWordBooks/add',
  edit='/books/inzWordBooks/edit',
  deleteOne = '/books/inzWordBooks/delete',
  deleteBatch = '/books/inzWordBooks/deleteBatch',
  importExcel = '/books/inzWordBooks/importExcel',
  exportXls = '/books/inzWordBooks/exportXls',
  inzWordBookChapterList = '/books/inzWordBooks/queryInzWordBookChapterByMainId',
  savePrompt = '/book_words/savePrompt',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzWordBookChapter = Api.inzWordBookChapterList
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
/**
 * 子表列表接口
 * @param params
 */
export const inzWordBookChapterList = (params) =>
  defHttp.get({url: Api.inzWordBookChapterList, params},{isTransformResponse:false});

/**
 * 保存提示词并获取hash
 * @param params SavePromptDto对象，包含prompt字段
 */
export const savePrompt = (params) => {
  return defHttp.post({ url: Api.savePrompt, params });
}
