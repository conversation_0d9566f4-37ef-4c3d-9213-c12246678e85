import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { defHttp } from '/@/utils/http/axios';
import { getModuleList } from '../inz_learning_modules/InzLearningModules.api';
import { getFirstLevelCategoriesByModule, getSecondLevelCategories } from '../inz_learning_categorys/InzLearningCategorys.api'; // 添加模块数据字典

// 添加模块数据字典
let moduleDict = [];
// 添加分类数据字典
let categoryDict = [];

// 预加载模块数据
defHttp
  .get({
    url: '/inz_learning_modules/inzLearningModules/list',
    params: { pageNo: 1, pageSize: 10000 },
  })
  .then((res) => {
    moduleDict = res?.records || [];
  })
  .catch((e) => console.error('模块数据加载失败:', e));

// 预加载分类数据
defHttp
  .get({
    url: '/inz_learning_categorys/inzLearningCategorys/list',
    params: { pageNo: 1, pageSize: 10000 },
  })
  .then((res) => {
    categoryDict = res?.records || [];
  })
  .catch((e) => console.error('分类数据加载失败:', e));

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '模块名称',
    align: 'center',
    dataIndex: 'moduleId', // 修改：从 'moduleName' 改为 'moduleId'
    width: 150,
    customRender: ({ text }) => {
      const module = moduleDict.find((item) => item.id === text);
      return module?.moduleName || text;
    },
  },
  {
    title: '分类名称',
    align: 'center',
    dataIndex: 'categoryId', // 保持不变
    width: 150,
    customRender: ({ text }) => {
      const category = categoryDict.find((item) => item.id === text);
      return category?.categoryName || text;
    },
  },
  {
    title: '视频标题',
    align: 'center',
    dataIndex: 'videoTitle',
  },
  {
    title: '视频描述',
    align: 'center',
    dataIndex: 'description',
  },
  {
    title: '视频封面',
    align: 'center',
    dataIndex: 'coverImage',
    customRender: render.renderImage,
  },
  {
    title: '视频文件',
    align: 'center',
    dataIndex: 'videoUrl',
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortOrder',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return text === 0 ? '禁用' : '启用';
    },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '模块名称',
    field: 'moduleName',
    component: 'Input',
  },
  {
    label: '分类名称',
    field: 'categoryName',
    component: 'Input',
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属模块',
    field: 'moduleId',
    component: 'ApiSelect',
    componentProps: {
      api: getModuleList,
      labelField: 'moduleName',
      valueField: 'id',
      resultField: 'records',
      placeholder: '请选择模块',
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.toLowerCase().includes(input.toLowerCase());
      },
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择所属模块!' }];
    },
  },
  {
    label: '一级分类',
    field: 'firstLevelCategoryId',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: formModel.moduleId ? () => getFirstLevelCategoriesByModule(formModel.moduleId) : () => Promise.resolve({ records: [] }),
        labelField: 'categoryName',
        valueField: 'id',
        resultField: 'records',
        placeholder: formModel.moduleId ? '请选择一级分类' : '请先选择模块',
        showSearch: true,
        disabled: !formModel.moduleId,
        filterOption: (input, option) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择一级分类!' }];
    },
  },
  {
    label: '所属分类',
    field: 'categoryId',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: formModel.firstLevelCategoryId ? () => getSecondLevelCategories(formModel.firstLevelCategoryId) : () => Promise.resolve({ records: [] }),
        labelField: 'categoryName',
        valueField: 'id',
        resultField: 'records', // 根据 sub-categories 接口的返回格式调整
        placeholder: formModel.firstLevelCategoryId ? '请选择二级分类' : '请先选择一级分类',
        showSearch: true,
        disabled: !formModel.firstLevelCategoryId,
        filterOption: (input, option) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择所属分类!' }];
    },
  },
  {
    label: '视频标题',
    field: 'videoTitle',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入视频标题!' }];
    },
  },
  {
    label: '视频描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '视频封面',
    field: 'coverImage',
    component: 'JImageUpload',
    componentProps: {
      fileMax: 1, // 只能上传一张封面图
      accept: '.jpg,.jpeg,.png,.gif,.webp', // 支持的图片格式
      maxSize: 10, // 最大文件大小10MB
      text: '点击上传封面图片',
      subText: '支持jpg、png等格式，建议尺寸16:9，不超过10MB',
    },
  },
  {
    label: '视频文件',
    field: 'videoUrl',
    component: 'ChunkVideoUpload',
    componentProps: {
      maxSize: 5120, // 最大文件大小5GB
      accept: '.mp4,.avi,.mov,.wmv,.flv,.webm', // 支持的视频格式
      chunkSize: 5, // 分片大小5MB
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请上传视频文件!' }];
    },
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
  },
  {
    label: '状态',
    field: 'status',
    component: 'InputNumber',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  moduleId: { title: '所属模块', order: 0, view: 'text', type: 'string' },
  categoryId: { title: '所属分类ID', order: 1, view: 'text', type: 'string' },
  videoTitle: { title: '视频标题', order: 2, view: 'text', type: 'string' },
  description: { title: '视频描述', order: 3, view: 'textarea', type: 'string' },
  coverImage: { title: '视频封面', order: 4, view: 'text', type: 'string' },
  videoUrl: {
    title: '视频文件',
    order: 5,
    view: 'textarea',
    type: 'string',
  },
  sortOrder: { title: '排序号', order: 6, view: 'number', type: 'number' },
  status: { title: '状态', order: 7, view: 'number', type: 'number' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
