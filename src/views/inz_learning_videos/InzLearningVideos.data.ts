import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属模块ID',
    align:"center",
    dataIndex: 'moduleId'
   },
   {
    title: '所属分类ID',
    align:"center",
    dataIndex: 'categoryId'
   },
   {
    title: '视频标题',
    align:"center",
    dataIndex: 'videoTitle'
   },
   {
    title: '视频描述',
    align:"center",
    dataIndex: 'description'
   },
   {
    title: '视频封面URL',
    align:"center",
    dataIndex: 'coverImage'
   },
   {
    title: '视频文件URL（JSON数组格式存储多个视频）',
    align:"center",
    dataIndex: 'videoUrl'
   },
   {
    title: '排序号',
    align:"center",
    dataIndex: 'sortOrder'
   },
   {
    title: '状态 0-禁用 1-启用',
    align:"center",
    dataIndex: 'status'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属模块ID',
    field: 'moduleId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所属模块ID!'},
          ];
     },
  },
  {
    label: '所属分类ID',
    field: 'categoryId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所属分类ID!'},
          ];
     },
  },
  {
    label: '视频标题',
    field: 'videoTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入视频标题!'},
          ];
     },
  },
  {
    label: '视频描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '视频封面URL',
    field: 'coverImage',
    component: 'Input',
  },
  {
    label: '视频文件URL（JSON数组格式存储多个视频）',
    field: 'videoUrl',
    component: 'InputTextArea',
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
  },
  {
    label: '状态 0-禁用 1-启用',
    field: 'status',
    component: 'InputNumber',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  moduleId: {title: '所属模块ID',order: 0,view: 'text', type: 'string',},
  categoryId: {title: '所属分类ID',order: 1,view: 'text', type: 'string',},
  videoTitle: {title: '视频标题',order: 2,view: 'text', type: 'string',},
  description: {title: '视频描述',order: 3,view: 'textarea', type: 'string',},
  coverImage: {title: '视频封面URL',order: 4,view: 'text', type: 'string',},
  videoUrl: {title: '视频文件URL（JSON数组格式存储多个视频）',order: 5,view: 'textarea', type: 'string',},
  sortOrder: {title: '排序号',order: 6,view: 'number', type: 'number',},
  status: {title: '状态 0-禁用 1-启用',order: 7,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}