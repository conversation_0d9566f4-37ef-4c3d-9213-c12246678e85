import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { h } from 'vue';
import { getModuleList } from '../inz_learning_modules/InzLearningModules.api';
import { getCategoryList, getCategoryListByModule } from '../inz_learning_categorys/InzLearningCategorys.api';
import ModuleNameRenderer from '../inz_learning_categorys/components/ModuleNameRenderer.vue';
import CategoryNameRenderer from '../inz_learning_categorys/components/CategoryNameRenderer.vue';

const VideoStatusMap = {
  0: '禁用',
  1: '启用',
};
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '所属模块ID',
    align: 'center',
    dataIndex: 'moduleId',
    width: 120,
  },
  {
    title: '模块名称',
    align: 'center',
    dataIndex: 'moduleId',
    width: 150,
    customRender: ({ text }) => {
      return h(ModuleNameRenderer, { moduleId: text });
    },
  },
  {
    title: '所属分类ID',
    align: 'center',
    dataIndex: 'categoryId',
    width: 120,
  },
  {
    title: '分类名称',
    align: 'center',
    dataIndex: 'categoryId',
    width: 150,
    customRender: ({ text }) => {
      return h(CategoryNameRenderer, { categoryId: text });
    },
  },
  {
    title: '视频标题',
    align: 'center',
    dataIndex: 'videoTitle',
  },
  {
    title: '视频描述',
    align: 'center',
    dataIndex: 'description',
  },
  {
    title: '视频封面URL',
    align: 'center',
    dataIndex: 'coverImage',
    customRender: render.renderImage,
  },
  {
    title: '视频文件URL',
    align: 'center',
    dataIndex: 'videoUrl',
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortOrder',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return VideoStatusMap[text];
    },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属模块',
    field: 'moduleId',
    component: 'ApiSelect',
    componentProps: {
      api: getModuleList,
      labelField: 'moduleName',
      valueField: 'id',
      placeholder: '请选择模块',
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.toLowerCase().includes(input.toLowerCase());
      },
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择所属模块!' }];
    },
  },
  {
    label: '所属分类',
    field: 'categoryId',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: formModel.moduleId ? () => getCategoryListByModule(formModel.moduleId) : getCategoryList,
        labelField: 'categoryName',
        valueField: 'id',
        placeholder: formModel.moduleId ? '请选择分类' : '请先选择模块',
        showSearch: true,
        disabled: !formModel.moduleId,
        filterOption: (input, option) => {
          return option.label.toLowerCase().includes(input.toLowerCase());
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择所属分类!' }];
    },
  },
  {
    label: '视频标题',
    field: 'videoTitle',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入视频标题!' }];
    },
  },
  {
    label: '视频描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '视频封面',
    field: 'coverImage',
    component: 'JImageUpload',
    componentProps: {
      fileMax: 1, // 只能上传一张封面图
      accept: '.jpg,.jpeg,.png,.gif,.webp', // 支持的图片格式
      maxSize: 10, // 最大文件大小10MB
      text: '点击上传封面图片',
      subText: '支持jpg、png等格式，建议尺寸16:9，不超过10MB',
    },
  },
  {
    label: '视频文件',
    field: 'videoUrl',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
  },
  {
    label: '状态 0-禁用 1-启用',
    field: 'status',
    component: 'InputNumber',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  moduleId: { title: '所属模块ID', order: 0, view: 'text', type: 'string' },
  categoryId: { title: '所属分类ID', order: 1, view: 'text', type: 'string' },
  videoTitle: { title: '视频标题', order: 2, view: 'text', type: 'string' },
  description: { title: '视频描述', order: 3, view: 'textarea', type: 'string' },
  coverImage: { title: '视频封面URL', order: 4, view: 'text', type: 'string' },
  videoUrl: {
    title: '视频文件URL',
    order: 5,
    view: 'textarea',
    type: 'string',
  },
  sortOrder: { title: '排序号', order: 6, view: 'number', type: 'number' },
  status: { title: '状态 0-禁用 1-启用', order: 7, view: 'number', type: 'number' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
