import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/user_books/inzUserBooks/list',
  save='/user_books/inzUserBooks/add',
  edit='/user_books/inzUserBooks/edit',
  deleteOne = '/user_books/inzUserBooks/delete',
  deleteBatch = '/user_books/inzUserBooks/deleteBatch',
  importExcel = '/user_books/inzUserBooks/importExcel',
  exportXls = '/user_books/inzUserBooks/exportXls',
  GetGoldBeanInfo = '/user_books/inzUserBooks/getGoldBean',
  GetBookPrice = '/user_books/inzUserBooks/getBookPrice',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}


// 获取用户金豆信息
export const getGoldBeanInfo = (params: { userId: string }) => {
  return defHttp.get({
    url: Api.GetGoldBeanInfo,
    params,
  });
};
// 单个停用
export const disableOne = (params) => {
  return defHttp.put({ url: '/user_books/inzUserBooks/disable', params });
}

// 批量修改状态
export const batchDisable = (params) => {
  return defHttp.put({ url: '/user_books/inzUserBooks/batchToggleStatus', params });
}
// 单个启用
export const enableOne = (params) => {
  return defHttp.put({ url: '/user_books/inzUserBooks/enable', params });
}

// 批量启用
export const batchEnable = (params) => {
  return defHttp.put({ url: '/user_books/inzUserBooks/batchEnable', params });
}
// 获取图书/系列价格
export const getBookPrice = (params: { bookId?: string; seriesId?: string }) => {
  return defHttp.get({
    url: Api.GetBookPrice,
    params,
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
