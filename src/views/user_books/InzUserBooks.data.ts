import { BasicColumn } from "/@/components/Table";
import { FormSchema } from "/@/components/Table";
import { rules } from "/@/utils/helper/validator";
import { render } from "/@/utils/common/renderUtils";
import { getWeekMonthQuarterYear } from "/@/utils";
import { defHttp } from "@/utils/http/axios";
import { useUserStore } from "/@/store/modules/user";
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { message } from "ant-design-vue";
dayjs.extend(customParseFormat);
// 预加载字典数据（在模块加载时执行）
let userDict = [];
let bookDict = [];
// 创建一个专门获取用户金豆的函数
// 加载用户字典
defHttp.get({ url: "/user_front/inzUserFront/listAll" })
  .then(res => {
    userDict = res || [];
  })
  .catch(e => console.error("用户数据加载失败:", e));

// 加载图书字典
defHttp.get({ url: "/books/inzWordBooks/listAll" })
  .then(res => {
    bookDict = res || [];
  })
  .catch(e => console.error("图书数据加载失败:", e));
//列表数据
export const columns: BasicColumn[] = [
  {
    title: "所属用户",
    align: "center",
    dataIndex: "createBy",
    customRender: ({ text }) => {
      // 从预加载数据中查找
      const user = userDict.find(item => item.id === text);
      return user?.realName || text;
    }
  },
  {
    title: "创建日期",
    align: "center",
    dataIndex: "createTime"
  },
  {
    title: "所属词书",
    align: "center",
    dataIndex: "wordBookId",
    customRender: ({ text }) => {
      const book = bookDict.find(item => item.id === text);
      return book?.name || text;
    }
  },
  {
    title: "状态",
    align: "center",
    dataIndex: "status",
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: "正在使用", value: "1" },
        { text: "未使用", value: "2" },
        { text: "停用", value: "3" },
      ]);
    }
  },
  {
    title: "开始日期",
    align: "center",
    dataIndex: "expirationStartData"
  },
  {
    title: "结束日期",
    align: "center",
    dataIndex: "expirationEndData"
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: "所属用户",
    field: 'createBy',
    component: 'ApiSelect',
    //colProps: {span: 6},
    componentProps: ({ formModel, formActionType }) => {
      return {
        api: () => defHttp.get({ url: "/user_front/inzUserFront/listAll" }),
        labelField: "realName",
        valueField: "id",
        resultField: "records",
        showSearch: true,
        filterOption: (input, option) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        placeholder: "请选择用户"
      };
    }
  },
];

// 计算总金豆的通用方法
const calculateTotalGoldenBeans = async (formModel) => {
  let total = 0;

  // 计算分类的金豆
  if (formModel.categoryIds && formModel.categoryIds.length > 0) {
    try {
      const categoryBooks = await defHttp.get({
        url: "/education/inzEducation/listByCategoryIds",
        params: { ids: formModel.categoryIds.join(",") }
      });
      total += categoryBooks.reduce((sum, book) => sum + (book.goldenBean || 0), 0);
    } catch (e) {
      console.error("获取分类图书失败:", e);
    }
  }

  // 计算单独选择图书的金豆
  if (formModel.wordBookIds && formModel.wordBookIds.length > 0) {
    try {
      const selectedBooks = await defHttp.get({
        url: "/books/inzWordBooks/listByIds",
        params: { ids: formModel.wordBookIds.join(",") }
      });
      total += selectedBooks.reduce((sum, book) => sum + (book.goldenBean || 0), 0);
    } catch (e) {
      console.error("获取图书详情失败:", e);
    }
  }

  formModel.totalGoldenBean = total;
};

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: "分配用户",
    field: "userIds",
    component: "ApiSelect",
    componentProps: ({ formModel }) => ({
      api: () => defHttp.get({ url: "/user_front/inzUserFront/listAll" }),
      mode: "multiple",
      labelField: "realName",
      valueField: "id",
      resultField: "records",
      showSearch: true,
      filterOption: (input, option) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
      placeholder: "请选择要分配的用户"
    }),
    rules: [
      { required: true, message: "请至少选择一个用户" },
      {
        validator: (_, value) => {
          if (value && value.length > 100) {
            return Promise.reject("单次最多分配100个用户");
          }
          return Promise.resolve();
        }
      }
    ]
  },
  {
    label: "选择图书分类",
    field: "categoryIds",
    component: "ApiSelect",
    componentProps: ({ formModel }) => ({
      api: () => defHttp.get({ url: "/education/inzEducation/rootList" }),
      mode: "multiple",
      labelField: "name",
      valueField: "id",
      resultField: "records",
      onChange: async (values) => {
        formModel.categoryIds = values || [];
        await calculateTotalGoldenBeans(formModel);
      }
    })
  },
  {
    label: "选择图书",
    field: "wordBookIds",
    component: "ApiSelect",
    componentProps: ({ formModel }) => ({
      api: () => defHttp.get({ url: "/books/inzWordBooks/listAll" }),
      mode: "multiple",
      labelField: "name",
      valueField: "id",
      resultField: "records",
      onChange: async (values) => {
        formModel.wordBookIds = values || [];
        await calculateTotalGoldenBeans(formModel);
      }
    })
  },
  {
    label: "所需金豆总数",
    field: "totalGoldenBean",
    component: "InputNumber",
    componentProps: {
      disabled: true,
      placeholder: "请选择图书或分类"
    },
    dynamicDisabled: true
  },
  // {
  //   label: "状态",
  //   field: "status",
  //   component: "Select",
  //   componentProps: {
  //     options: [
  //       { label: "正在使用", value: 1 },
  //       { label: "未使用", value: 2 }
  //     ],
  //     placeholder: "请选择状态"
  //   }
  // },
  {
    label: "有效期",
    field: "timeRange",
    component: "RangePicker",
    componentProps: {
      showTime: false,
      valueType: 'Date',
      format: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD",
      placeholder: ["开始时间", "结束时间"],
      ranges: {
        '1个月': [dayjs(), dayjs().add(1, 'month')],
        '3个月': [dayjs(), dayjs().add(3, 'month')],
        '6个月': [dayjs(), dayjs().add(6, 'month')],
        '1年': [dayjs(), dayjs().add(1, 'year')]
      }
    },
    rules: [
      { required: true, message: "请选择有效期范围" },
      {
        validator: (_, value) => {
          if (!value?.[0] || !value?.[1]) {
            return Promise.reject("请选择完整时间范围");
          }

          const start = dayjs(value[0]);
          const end = dayjs(value[1]);

          // 验证时间顺序
          if (start.isAfter(end)) {
            return Promise.reject("结束时间必须晚于开始时间");
          }

          // 验证不超过1年
          if (end.diff(start, 'day') > 365) {
            return Promise.reject("有效期最长不能超过1年");
          }

          return Promise.resolve();
        }
      }
    ]
  },
  {
    label: "",
    field: "id",
    component: "Input",
    show: false
  }
];

// 高级查询数据
export const superQuerySchema = {
  createBy: { title: "用户名", order: 0, view: "text", type: "string" },
  createTime: { title: "创建日期", order: 1, view: "datetime", type: "string" },
  wordBookId: { title: "图书id", order: 2, view: "text", type: "string" },
  status: { title: "状态 1使用 2停用", order: 3, view: "number", type: "number" }
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
