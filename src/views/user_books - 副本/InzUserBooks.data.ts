import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { defHttp } from "@/utils/http/axios";
// 预加载字典数据（在模块加载时执行）
let userDict = [];
let bookDict = [];

// 加载用户字典
defHttp.get({ url: '/user_front/inzUserFront/listAll' })
  .then(res => {
    userDict = res || [];
  })
  .catch(e => console.error('用户数据加载失败:', e));

// 加载图书字典
defHttp.get({ url: '/books/inzWordBooks/listAll' })
  .then(res => {
    bookDict = res || [];
  })
  .catch(e => console.error('图书数据加载失败:', e));
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '用户名',
    align:"center",
    dataIndex: 'createBy',
     customRender: ({ text }) => {
       // 从预加载数据中查找
       const user = userDict.find(item => item.id === text);
       return user?.realName || text;
     }
   },
   {
    title: '创建日期',
    align:"center",
    dataIndex: 'createTime'
   },
   {
    title: '图书id',
    align:"center",
    dataIndex: 'wordBookId',
     customRender: ({ text }) => {
       const book = bookDict.find(item => item.id === text);
       return book?.name || text;
     }
   },
   {
    title: '状态',
    align:"center",
    dataIndex: 'status',
     customRender: ({ text }) => {
       return render.renderSwitch(text, [{ text: "正在使用", value: "1" }, { text: "未使用", value: "2" }]);
     }
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '用户名',
    field: 'createBy',
    component: 'ApiSelect',
    componentProps:({ formModel, formActionType }) =>  {
      return {
        api: () => defHttp.get({ url: '/user_front/inzUserFront/listAll' }),
        //数值转成String
        numberToString: false,
        //标题字段
        labelField: 'realName',
        //值字段
        valueField: 'id',
        //请求参数
        params: {},
        //返回结果字段
        resultField: 'records',
      }
    },
  },
  {
    label: '选择图书',
    field: 'wordBookId',
    component: 'ApiSelect',
    componentProps:({ formModel, formActionType }) =>  {
      return {
        api: () => defHttp.get({ url: '/books/inzWordBooks/listAll' }),
        //数值转成String
        numberToString: false,
        //标题字段
        labelField: 'name',
        //值字段
        valueField: 'id',
        //请求参数
        params: {},
        //返回结果字段
        resultField: 'records',
      }
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正在使用', value: 1 },
        { label: '未使用', value: 2 },
      ],
      placeholder: '请选择生成状态',
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  createBy: {title: '用户名',order: 0,view: 'text', type: 'string',},
  createTime: {title: '创建日期',order: 1,view: 'datetime', type: 'string',},
  wordBookId: {title: '图书id',order: 2,view: 'text', type: 'string',},
  status: {title: '状态 1使用 2停用',order: 3,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
