import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属部门',
    align:"center",
    dataIndex: 'deptName'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '员工名称',
    align:"center",
    dataIndex: 'name'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属部门',
    field: 'deptName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入所属部门!'},
          ];
     },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"sex"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入性别!'},
          ];
     },
  },
  {
    label: '员工名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入员工名称!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表单数据
//子表列表数据
export const empColumns: BasicColumn[] = [
   {
    title: '所属部门',
    align:"center",
    dataIndex: 'deptName'
   },
   {
    title: '员工id',
    align:"center",
    dataIndex: 'empId'
   },
];
export const empFormSchema: FormSchema[] = [
  {
    label: '所属部门',
    field: 'deptName',
    component: 'Input',
  },
  {
    label: '员工id',
    field: 'empId',
    component: 'Input',
  },
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表表格配置

// 高级查询数据
export const superQuerySchema = {
  deptName: {title: '所属部门',order: 0,view: 'text', type: 'string',},
  sex: {title: '性别',order: 1,view: 'list', type: 'string',dictCode: 'sex',},
  name: {title: '员工名称',order: 2,view: 'text', type: 'string',},
  //子表高级查询
  emp: {
    title: '员工',
    view: 'table',
    fields: {
        deptName: {title: '所属部门',order: 0,view: 'text', type: 'string',},
        empId: {title: '员工id',order: 1,view: 'text', type: 'string',},
    }
  },
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}