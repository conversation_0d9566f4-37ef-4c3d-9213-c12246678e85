import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/inz_coach_class/inzCoachClass/list',
  save='/inz_coach_class/inzCoachClass/add',
  edit='/inz_coach_class/inzCoachClass/edit',
  deleteOne = '/inz_coach_class/inzCoachClass/delete',
  deleteBatch = '/inz_coach_class/inzCoachClass/deleteBatch',
  importExcel = '/inz_coach_class/inzCoachClass/importExcel',
  exportXls = '/inz_coach_class/inzCoachClass/exportXls',
  getCoachList = '/inz_coach_class/inzCoachClass/getCoachList',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 获取教练列表
 * @param params
 */
export const getCoachList = (params) => {
  // 使用用户列表接口，筛选角色为教练的用户
  return defHttp.get({
    url: '/user_front/inzUserFront/list',
    params: {
      ...params,
      role: 'coach' // 筛选教练角色
    }
  });
}
