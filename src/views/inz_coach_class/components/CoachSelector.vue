<template>
  <a-select
    v-model:value="selectedCoachName"
    placeholder="请选择教练"
    :options="coachOptions"
    show-search
    :filter-option="filterCoachOption"
    allowClear
    @change="handleCoachChange"
    :loading="loading"
  >
    <template #option="{ value, label, coachId, phone, status, role }">
      <div class="coach-option">
        <div class="coach-name">{{ label }}</div>
        <div class="coach-details">
          <span class="coach-id">ID: {{ coachId }}</span>
          <span class="coach-phone" v-if="phone">电话: {{ phone }}</span>
          <a-tag size="small" color="blue">教练</a-tag>
          <a-tag size="small" :color="status === 1 ? 'green' : 'red'">
            {{ status === 1 ? '启用' : '停用' }}
          </a-tag>
        </div>
      </div>
    </template>
  </a-select>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { getCoachList } from '../InzCoachClass.api';
import { message } from 'ant-design-vue';

// 组件属性
interface Props {
  value?: string;
  placeholder?: string;
}

// 组件事件
interface Emits {
  (e: 'update:value', value: string): void;
  (e: 'change', value: string, coachId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择教练'
});

const emit = defineEmits<Emits>();

// 响应式数据
const selectedCoachName = ref<string>('');
const coachOptions = ref<any[]>([]);
const loading = ref(false);

// 监听外部值变化
watch(() => props.value, (newValue) => {
  selectedCoachName.value = newValue || '';
}, { immediate: true });

// 加载教练列表
const loadCoachList = async () => {
  loading.value = true;
  try {
    const res = await getCoachList({ pageSize: 9999 }); // 获取所有教练
    console.log('教练列表API返回数据:', res); // 添加调试日志

    if (res && res.records && res.records.length > 0) {
      coachOptions.value = res.records.map((coach: any) => {
        console.log('处理教练数据:', coach); // 添加调试日志
        const coachName = coach.realName || coach.username || coach.coachName || coach.name || '未知教练';
        return {
          label: coachName,
          value: coachName,
          coachId: coach.id,
          phone: coach.phone || '',
          status: coach.status || 1,
          role: coach.role || 'coach',
          ...coach
        };
      });
      console.log('处理后的教练选项:', coachOptions.value); // 添加调试日志
    } else {
      console.warn('教练列表为空或数据格式不正确:', res);
      coachOptions.value = [];
    }
  } catch (error) {
    console.error('获取教练列表失败:', error);
    message.error('获取教练列表失败');
    coachOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 教练搜索过滤
const filterCoachOption = (input: string, option: any) => {
  const searchText = input.toLowerCase();
  return (
    option.label.toLowerCase().includes(searchText) ||
    option.coachId.toString().toLowerCase().includes(searchText) ||
    (option.phone && option.phone.toLowerCase().includes(searchText))
  );
};

// 处理教练选择变化
const handleCoachChange = (value: string) => {
  selectedCoachName.value = value;
  
  // 查找选中的教练信息
  const selectedCoach = coachOptions.value.find(coach => coach.value === value);
  const coachId = selectedCoach ? selectedCoach.coachId : '';
  
  // 触发事件
  emit('update:value', value);
  emit('change', value, coachId);
  
  console.log('教练选择变化:', {
    coachName: value,
    coachId: coachId,
    coachInfo: selectedCoach
  });
};

// 组件挂载时加载数据
onMounted(() => {
  loadCoachList();
});

// 暴露方法给父组件
defineExpose({
  loadCoachList,
  getSelectedCoach: () => {
    return coachOptions.value.find(coach => coach.value === selectedCoachName.value);
  }
});
</script>

<style lang="less" scoped>
.coach-option {
  padding: 8px 0;
  
  .coach-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }
  
  .coach-details {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .coach-id {
      font-size: 12px;
      color: #8c8c8c;
    }
    
    .coach-phone {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}

:deep(.ant-select-dropdown) {
  .ant-select-item-option-content {
    padding: 0;
  }
}
</style>
