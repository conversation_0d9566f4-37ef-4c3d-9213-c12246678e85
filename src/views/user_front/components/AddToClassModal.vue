<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    destroyOnClose
    :title="modalTitle"
    :width="600"
    @ok="handleSubmit"
  >
    <div class="add-to-class-container">
      <!-- 用户信息显示 -->
      <a-alert
        :message="`将用户 ${selectedUser?.realName || selectedUser?.username || '未知用户'} 添加到班级`"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <a-form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 选择班级 -->
        <a-form-item
          label="选择班级"
          name="classId"
          :rules="[{ required: true, message: '请选择要加入的班级' }]"
        >
          <a-select
            v-model:value="formState.classId"
            placeholder="请选择班级"
            :options="classOptions"
            show-search
            :filter-option="filterClassOption"
            allowClear
            @change="handleClassChange"
            :loading="loading"
          >
            <template #option="{ value, label, coachName, currentStudents, description }">
              <div class="class-option">
                <div class="class-name">{{ label }}</div>
                <div class="class-details">
                  <span class="coach-name">教练: {{ coachName }}</span>
                  <span class="student-count">学生数: {{ currentStudents || 0 }}</span>
                </div>
                <div class="class-description" v-if="description">{{ description }}</div>
              </div>
            </template>
          </a-select>
        </a-form-item>

        <!-- 选中班级信息展示 -->
        <a-form-item v-if="selectedClass" label="班级信息">
          <div class="selected-class-info">
            <div class="info-item">
              <span class="label">班级名称:</span>
              <span class="value">{{ selectedClass.className }}</span>
            </div>
            <div class="info-item">
              <span class="label">教练姓名:</span>
              <span class="value">{{ selectedClass.coachName }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前学生:</span>
              <span class="value">{{ selectedClass.currentStudents || 0 }} 人</span>
            </div>
            <div class="info-item" v-if="selectedClass.description">
              <span class="label">班级描述:</span>
              <span class="value">{{ selectedClass.description }}</span>
            </div>
          </div>
        </a-form-item>

        <!-- 用户信息确认 -->
        <a-form-item label="用户信息">
          <div class="user-info">
            <div class="info-item">
              <span class="label">姓名:</span>
              <span class="value">{{ selectedUser?.realName || selectedUser?.username }}</span>
            </div>
            <div class="info-item">
              <span class="label">手机号:</span>
              <span class="value">{{ selectedUser?.phone || '无' }}</span>
            </div>
            <div class="info-item">
              <span class="label">角色:</span>
              <span class="value">{{ getRoleDisplayName(selectedUser?.role) }}</span>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import { addUserToClass, getClassList } from '../InzUserFront.api';
import { getRoleDisplayName } from '../InzUserFront.data';

// 组件事件
const emit = defineEmits(['register', 'success']);

// 响应式数据
const formRef = ref();
const loading = ref(false);
const selectedUser = ref<any>(null);
const classOptions = ref<any[]>([]);
const selectedClass = ref<any>(null);

// 表单状态
const formState = reactive({
  classId: ''
});

// 模态框标题
const modalTitle = computed(() => {
  return `添加到班级 - ${selectedUser.value?.realName || selectedUser.value?.username || '用户'}`;
});

// 注册模态框
const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
  selectedUser.value = data.record;
  
  // 重置表单
  formState.classId = '';
  selectedClass.value = null;
  
  // 加载我的班级列表
  await loadMyClassList();
});

// 加载我的班级列表
const loadMyClassList = async () => {
  loading.value = true;
  try {
    // 使用班级列表接口，后端会根据当前用户权限返回对应的班级
    const res = await getClassList({ pageSize: 9999 }); // 获取班级列表
    console.log('班级列表API返回:', res); // 添加调试日志

    if (res && res.records && res.records.length > 0) {
      classOptions.value = res.records.map((classItem: any) => {
        console.log('处理班级数据:', classItem); // 添加调试日志
        return {
          label: classItem.className || '未知班级',
          value: classItem.id,
          coachName: classItem.coachName || '未知教练',
          currentStudents: classItem.currentStudents || 0,
          description: classItem.description || '',
          className: classItem.className,
          ...classItem
        };
      });
      console.log('处理后的班级选项:', classOptions.value); // 添加调试日志
    } else {
      console.warn('班级列表为空:', res);
      classOptions.value = [];
      message.info('您还没有创建任何班级');
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    message.error('获取班级列表失败');
    classOptions.value = [];
  } finally {
    loading.value = false;
  }
};

// 班级搜索过滤
const filterClassOption = (input: string, option: any) => {
  const searchText = input.toLowerCase();
  return (
    option.label.toLowerCase().includes(searchText) ||
    option.coachName.toLowerCase().includes(searchText)
  );
};

// 处理班级选择变化
const handleClassChange = (value: string) => {
  selectedClass.value = classOptions.value.find(item => item.value === value);
  console.log('选择班级:', selectedClass.value);
};

// 表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();
    
    if (!formState.classId) {
      message.warning('请选择班级');
      return;
    }
    
    if (!selectedUser.value?.id) {
      message.warning('用户信息异常');
      return;
    }

    setModalProps({ confirmLoading: true });
    
    // 调用添加用户到班级API
    const params = {
      classId: formState.classId,
      userId: selectedUser.value.id
    };
    
    await addUserToClass(params);
    
    message.success(`成功将用户 ${selectedUser.value.realName || selectedUser.value.username} 添加到班级`);
    closeModal();
    emit('success');
    
  } catch (error) {
    console.error('添加用户到班级失败:', error);
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    message.error('添加用户到班级失败: ' + (error.message || '未知错误'));
  } finally {
    setModalProps({ confirmLoading: false });
  }
};
</script>

<style lang="less" scoped>
.add-to-class-container {
  padding: 16px 0;
}

.class-option {
  padding: 8px 0;
  
  .class-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }
  
  .class-details {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
    
    .coach-name, .student-count {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
  
  .class-description {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }
}

.selected-class-info, .user-info {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  
  .info-item {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      width: 80px;
      color: #666;
      font-size: 14px;
    }
    
    .value {
      flex: 1;
      color: #262626;
      font-size: 14px;
    }
  }
}

:deep(.ant-select-dropdown) {
  .ant-select-item-option-content {
    padding: 0;
  }
}
</style>
