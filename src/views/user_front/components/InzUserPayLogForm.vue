<template>
    <BasicForm @register="registerForm" name="InzUserPayLogForm" class="basic-modal-form" />
</template>
<script lang="ts">
    import {defineComponent} from 'vue';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {inzUserPayLogFormSchema} from '../InzUserFront.data';
    import {defHttp} from '/@/utils/http/axios';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'

    export default defineComponent({
        name:"InzUserPayLogForm",
        components: {BasicForm},
        emits:['register'],
        props:{
            disabled: {
                type: Boolean,
                default: false
            }
        },
        setup(props,{emit}) {
            const [registerForm, { setProps, resetFields, setFieldsValue, getFieldsValue, validate, scrollToField }] = useForm({
                labelWidth: 150,
                schemas: inzUserPayLogFormSchema,
                showActionButtonGroup: false,
                baseColProps: {span: 24}
            });
            /**
            *初始化加载数据
            */
            function initFormData(url,id){
                if(id){
                     defHttp.get({url,params:{id}},{isTransformResponse:false}).then(res=>{
                       if (res.success && res.result.records[0]) {
                         // 获取用户信息添加真实姓名
                         const payLogData = res.result.records[0];
                         if (payLogData.userId) {
                           defHttp.get({url: '/user_front/inzUserFront/queryById', params: {id: payLogData.userId}}).then(userData => {
                             if (userData) {
                               payLogData.realName = userData.realName || '未知';
                               setFieldsValue({...payLogData});
                             } else {
                               setFieldsValue({...payLogData});
                             }
                           }).catch(() => {
                             setFieldsValue({...payLogData});
                           });
                         } else {
                           setFieldsValue({...payLogData});
                         }
                       }
                    })
                }
                setProps({disabled: props.disabled})
            }

            /**
             *获取表单数据
             */
            function getFormData(){
                let formData = getFieldsValue();
                // 确保表单数据包含realName字段
                if (formData.userId && !formData.realName) {
                    // 如果没有realName但有userId，尝试获取用户信息
                    defHttp.get({url: '/user_front/inzUserFront/queryById', params: {id: formData.userId}}).then(userData => {
                        if (userData && userData.realName) {
                            formData.realName = userData.realName;
                        }
                    }).catch(() => {
                        console.error('获取用户真实姓名失败');
                    });
                }
                
                Object.keys(formData).map(k=>{
                    if(formData[k] instanceof Array){
                        formData[k] = formData[k].join(',')
                    }
                });
                return [formData];
            }

            /**
            *表单校验
            */
            function validateForm(index){
                return new Promise((resolve, reject) => {
                    // 验证子表表单
                    validate().then(()=>{
                        return resolve()
                    }).catch(({ errorFields })=> {
                        return reject({ error: VALIDATE_FAILED ,index, errorFields: errorFields, scrollToField: scrollToField })
                    })
                })
            }
            return {
                registerForm,
                resetFields,
                initFormData,
                getFormData,
                validateForm
            }
        }
    })
</script>
<style lang="less" scoped>
  .basic-modal-form {
    overflow: auto;
    height: 340px;
  }
</style>
