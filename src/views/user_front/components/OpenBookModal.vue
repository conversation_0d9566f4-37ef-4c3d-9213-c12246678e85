<template>
  <BasicModal
    @register="registerModal"
    @ok="handleSubmit"
    :width="500"
    title="为用户开通单词书"
    :titleStyle="{ color: '#1890ff', fontSize: '18px' }"
    :bodyStyle="{ padding: '24px' }"
  >
    <div class="open-book-container">
      <a-spin :spinning="loading">
        <!-- 表单区域 -->
        <a-form :model="formState" layout="vertical">
          <!-- 用户真实姓名 -->
          <a-form-item
            label="选择用户"
            :rules="[{ required: true, message: '请选择要开通的用户' }]"
          >
            <a-select
              v-model:value="formState.userName"
              placeholder="请选择要开通的用户"
              :options="userOptions"
              show-search
              :filter-option="filterUserOption"
              allowClear
              @change="handleUserChange"
            >
              <template #option="{ value, label, phone, role }">
                <div class="user-option">
                  <div class="user-name">{{ label }}</div>
                  <div class="user-details">
                    <span class="user-phone">{{ phone }}</span>
                    <a-tag size="small" :color="getRoleColor(role)">{{ role }}</a-tag>
                  </div>
                </div>
              </template>
            </a-select>
          </a-form-item>

          <!-- 教育阶段 -->
          <a-form-item
            label="教育阶段"
            :rules="[{ required: true, message: '请选择教育阶段' }]"
          >
            <a-select
              v-model:value="formState.educationName"
              placeholder="请选择教育阶段"
              :options="educationOptions"
              @change="handleEducationChange"
              allowClear
            />
          </a-form-item>

          <!-- 费用提示 -->
          <a-alert
            v-if="selectedEducation"
            type="info"
            :message="`将消耗 ${selectedEducation.goldenBean || 0} 金豆`"
            class="mb-4 mt-4"
            show-icon
          />
          
          <!-- 错误提示 -->
          <a-alert
            v-if="errorMsg"
            type="error"
            :message="errorMsg"
            class="mb-4 mt-4"
            show-icon
          />
        </a-form>
      </a-spin>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { openBookForUser, getEducationList, list } from '../InzUserFront.api';
import { useUserStore } from '/@/store/modules/user';
import { message } from 'ant-design-vue';

const userStore = useUserStore();
const loading = ref(false);
const errorMsg = ref('');
const educationOptions = ref<any[]>([]);
const selectedEducation = ref<any>(null);
const userOptions = ref<any[]>([]);
const selectedUser = ref<any>(null);

// 获取当前用户姓名的函数
const getCurrentUserName = () => {
  const userInfo = userStore.getUserInfo;
  console.log('用户store信息:', userInfo);
  return userInfo?.realName || userInfo?.username || '';
};

// 表单状态
const formState = reactive({
  userName: '', // 要开通的用户真实姓名
  educationName: '', // 教育阶段名称
  ruserName: getCurrentUserName(), // 当前登录的代理商真实姓名
});

// 注册模态框
const [registerModal, { closeModal }] = useModalInner(() => {
  // 重置表单状态
  formState.userName = '';
  formState.educationName = '';
  formState.ruserName = getCurrentUserName(); // 重新设置当前用户姓名
  errorMsg.value = '';
  selectedEducation.value = null;
  selectedUser.value = null;

  // 加载数据
  loadEducationData();
  loadUserData();
});

const emit = defineEmits(['success']);

// 加载教育阶段数据
const loadEducationData = async () => {
  loading.value = true;
  try {
    const res = await getEducationList({});
    if (res && res.records) {
      educationOptions.value = res.records.map((item: any) => ({
        label: item.name,
        value: item.name,
        ...item
      }));
    }
  } catch (error) {
    console.error('获取教育阶段失败:', error);
    message.error('获取教育阶段失败');
  } finally {
    loading.value = false;
  }
};

// 加载用户数据
const loadUserData = async () => {
  loading.value = true;
  try {
    const res = await list({ pageSize: 9999 }); // 获取所有用户
    if (res && res.records) {
      userOptions.value = res.records.map((user: any) => ({
        label: user.realName || user.username || '未知用户',
        value: user.realName || user.username || '未知用户',
        phone: user.phone || '无手机号',
        role: user.role || '普通用户',
        userId: user.id,
        ...user
      }));
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    message.error('获取用户列表失败');
  } finally {
    loading.value = false;
  }
};

// 用户搜索过滤
const filterUserOption = (input: string, option: any) => {
  const searchText = input.toLowerCase();
  return (
    option.label.toLowerCase().includes(searchText) ||
    option.phone.toLowerCase().includes(searchText)
  );
};

// 获取角色颜色
const getRoleColor = (role: string) => {
  const roleColors = {
    '创始人': 'purple',
    '渠道商': 'blue',
    '区域合伙人': 'green',
    '城市合伙人': 'orange',
    '省级合伙人': 'red',
    '管理员': 'volcano',
    '系统管理员': 'magenta',
    '普通用户': 'default',
    'VIP用户': 'gold'
  };
  return roleColors[role] || 'blue';
};

// 选择用户时触发
const handleUserChange = (value: string) => {
  selectedUser.value = userOptions.value.find(item => item.value === value);
  errorMsg.value = '';
};

// 选择教育阶段时触发
const handleEducationChange = (value: string) => {
  selectedEducation.value = educationOptions.value.find(item => item.value === value);
  errorMsg.value = '';
};

// 提交处理
const handleSubmit = async () => {
  // 表单验证
  if (!formState.userName) {
    errorMsg.value = '请输入用户真实姓名';
    return;
  }
  
  if (!formState.educationName) {
    errorMsg.value = '请选择教育阶段';
    return;
  }
  
  loading.value = true;

  // 添加调试日志
  console.log('开通单词书参数:', {
    userName: formState.userName,
    educationName: formState.educationName,
    ruserName: formState.ruserName
  });
  console.log('当前用户信息:', userStore.getUserInfo);

  try {
    const res = await openBookForUser({
      userName: formState.userName,
      educationName: formState.educationName,
      ruserName: formState.ruserName
    });
    
    message.success('开通单词书成功');
    emit('success');
    closeModal();
  } catch (err: any) {
    console.error('开通单词书失败:', err);
    errorMsg.value = err?.message || '开通单词书失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 组件挂载后加载数据
onMounted(() => {
  loadEducationData();
  loadUserData();
});
</script>

<style lang="less" scoped>
.open-book-container {
  padding: 16px;
}

.user-option {
  padding: 8px 0;

  .user-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .user-details {
    display: flex;
    align-items: center;
    gap: 8px;

    .user-phone {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
}

:deep(.ant-select-dropdown) {
  .ant-select-item-option-content {
    padding: 0;
  }
}
</style>
