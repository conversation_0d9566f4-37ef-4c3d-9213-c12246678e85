<template>
  <BasicModal
    @register="registerModal"
    @ok="handleSubmit"
    :width="500"
    :title="`${modalData.isBatch ? '批量' : ''}调整金豆`"
    :titleStyle="{ color: '#1890ff', fontSize: '18px' }"
    :bodyStyle="{ padding: '24px' }"
  >
    <div class="beans-adjust-container">
      <!-- 批量操作提示 -->
      <a-alert
        v-if="modalData.isBatch"
        type="info"
        :message="`将影响 ${modalData.userIds.length} 位用户`"
        class="mb-4"
        show-icon
      />

      <!-- 调整数量表单 -->
      <a-form layout="vertical">
        <a-form-item
          label="调整数量"
          :validate-status="amountStatus"
          :help="amountHelpText"
        >
          <a-input-number
            v-model:value="formState.amount"
            :min="-999999"
            :max="999999"
            :step="100"
            :formatter="formatAmount"
            :parser="parseAmount"
            placeholder="输入正数增加/负数减少"
            style="width: 100%"
            size="large"
            @change="validateAmount"
          />
          <div class="amount-tips mt-2">
            <span>当前值: {{ formattedAmount }}</span>
            <a-tag :color="amountTagColor" class="ml-2">
              {{ amountTagText }}
            </a-tag>
          </div>
        </a-form-item>

        <a-form-item
          label="修改原因"
          :validate-status="reasonStatus"
          :help="reasonHelpText"
        >
          <a-textarea
            v-model:value="formState.reason"
            placeholder="请输入金豆调整的原因（必填）"
            :rows="3"
            :maxlength="200"
            show-count
            @change="validateReason"
          />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { assignBeans } from "@/views/user_front/InzUserFront.api";
import { message } from "ant-design-vue";

// 类型定义
interface ModalData {
  isBatch: boolean;
  userIds: string[];
}

// 模态框数据（替代props）
const modalData = ref<ModalData>({
  isBatch: false,
  userIds: []
});

// 表单状态
const formState = ref({
  amount: 100,
  reason: ''
});

// 金额格式化显示
const formatAmount = (value: number) => {
  return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 金额解析
const parseAmount = (value: string) => {
  return value.replace(/\$\s?|(,*)/g, '');
};

// 计算属性
const formattedAmount = computed(() => formatAmount(formState.value.amount));
const amountTagColor = computed(() =>
  formState.value.amount > 0 ? 'green' :
    formState.value.amount < 0 ? 'red' : 'default'
);
const amountTagText = computed(() =>
  formState.value.amount > 0 ? '增加金豆' :
    formState.value.amount < 0 ? '扣除金豆' : '无变化'
);

// 校验状态
const amountStatus = ref<'success' | 'error' | ''>('');
const amountHelpText = ref('');
const reasonStatus = ref<'success' | 'error' | ''>('');
const reasonHelpText = ref('');

// 金额校验
const validateAmount = () => {
  if (formState.value.amount === 0) {
    amountStatus.value = 'error';
    amountHelpText.value = '调整值不能为0';
  } else {
    amountStatus.value = '';
    amountHelpText.value = '';
  }
};

// 原因校验
const validateReason = () => {
  if (!formState.value.reason || formState.value.reason.trim() === '') {
    reasonStatus.value = 'error';
    reasonHelpText.value = '请输入修改原因';
  } else {
    reasonStatus.value = '';
    reasonHelpText.value = '';
  }
};

// 注册模态框
const [registerModal, { closeModal }] = useModalInner((data: ModalData) => {
  console.log('接收到模态框数据:', data);
  modalData.value = {
    isBatch: data.isBatch,
    userIds: data.userIds || []
  };
  // 重置表单状态
  formState.value.amount = 100;
  formState.value.reason = '';
  amountStatus.value = '';
  reasonStatus.value = '';
  amountHelpText.value = '';
  reasonHelpText.value = '';
});

const emit = defineEmits(['success']);

// 提交处理
const handleSubmit = async () => {
  validateAmount();
  validateReason();

  if (amountStatus.value === 'error' || reasonStatus.value === 'error') {
    return;
  }

  try {
    console.log('提交数据:', {
      userIds: modalData.value.userIds,
      amount: formState.value.amount,
      reason: formState.value.reason
    });

    await assignBeans({
      isBatch: modalData.value.isBatch,
      userIds: modalData.value.userIds,
      amount: formState.value.amount,
      reason: formState.value.reason
    });

    message.success(
      modalData.value.isBatch
        ? `已批量调整${modalData.value.userIds.length}位用户的金豆`
        : '金豆调整成功'
    );
    emit('success');
    closeModal();
  } catch (e) {
    console.error('操作失败:', e);
    message.error(`操作失败: ${e.message || '未知错误'}`);
  }
};
</script>

<style lang="less" scoped>
.beans-adjust-container {
  padding: 16px;

  :deep(.ant-input-number) {
    width: 100%;
    height: 48px;
    font-size: 16px;

    .ant-input-number-input {
      height: 100%;
      font-size: 16px;
    }

    &-handler-wrap {
      opacity: 1;
      height: 100%;
    }
  }

  .amount-tips {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;

    .ant-tag {
      margin-left: 8px;
    }
  }
}
</style>
