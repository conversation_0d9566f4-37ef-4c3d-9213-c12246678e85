<template>
  <div>
    <BasicForm @register="registerForm" ref="formRef"/>
  <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <a-tab-pane tab="用户登录设备" key="inzUserDevice" :forceRender="true">
        <JVxeTable
          v-if="inzUserDeviceTable.show"      
          keep-source
          resizable
          ref="inzUserDevice"
          :loading="inzUserDeviceTable.loading"
          :columns="inzUserDeviceTable.columns"
          :dataSource="inzUserDeviceTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
          />
      </a-tab-pane>
      <a-tab-pane tab="用户金豆记录" key="inzUserPayLog" :forceRender="true">
        <InzUserPayLogForm ref="inzUserPayLogForm" :disabled="formDisabled"></InzUserPayLogForm>
      </a-tab-pane>

    </a-tabs>
    <div style="width: 100%;text-align: center;margin-top: 10px;" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
    import { defHttp } from '/@/utils/http/axios';
    import {ref, computed, unref,reactive, onMounted, defineProps } from 'vue';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { JVxeTable } from '/@/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    import InzUserPayLogForm from './InzUserPayLogForm.vue'
    import {formSchema,inzUserDeviceJVxeColumns} from '../InzUserFront.data';
    import {saveOrUpdate,queryInzUserDevice,queryInzUserPayLog} from '../InzUserFront.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    const isUpdate = ref(true);
    
    const refKeys = ref(['inzUserDevice', 'inzUserPayLog', ]);
    const activeKey = ref('inzUserDevice');
    const inzUserDevice = ref();
    const inzUserPayLogForm = ref();
    const tableRefs = {inzUserDevice, };
    const inzUserDeviceTable = reactive({
          loading: false,
          dataSource: [],
          columns:inzUserDeviceJVxeColumns,
          show: false
    })

    const props = defineProps({
      formData: { type: Object, default: ()=>{} },
      formBpm: { type: Boolean, default: true }
    });
    const formDisabled = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return false;
        }
      }
      return true;
    });
    // 是否显示提交按钮
    const showFlowSubmitButton = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return true
        }
      }
      return false
    });
    
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });

    onMounted(()=>{
      initFormData();
    });
    //渲染流程表单数据
    const queryByIdUrl = '/user_front/inzUserFront/queryById';
    async function initFormData(){
      if(props.formBpm === true){
        await reset();
        let params = {id: props.formData.dataId};
        const data = await defHttp.get({url: queryByIdUrl, params});
        //表单赋值
        await setFieldsValue({
          ...data
        });
        inzUserPayLogForm.value.initFormData(queryInzUserPayLog, data.id)
        requestSubTableData(queryInzUserDevice, {id: data.id}, inzUserDeviceTable, ()=>{
          inzUserDeviceTable.show = true;
        });
        // 隐藏底部时禁用整个表单
        setProps({ disabled: formDisabled.value })
      }
    }
    
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys,validateSubForm);

    async function reset(){
      await resetFields();
      activeKey.value = 'inzUserDevice';
      inzUserDeviceTable.dataSource = [];
      inzUserPayLogForm.value.resetFields();
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue);
         // 获取金豆记录数据
         const payLogData = inzUserPayLogForm.value.getFormData();
         const hasPayLogData = payLogData && payLogData.length > 0 && 
                             (payLogData[0].content || payLogData[0].goldenBean || payLogData[0].type);
         
         // 如果有金豆数据且没有realName，从主表单中获取
         if (hasPayLogData && !payLogData[0].realName && main.realName) {
           payLogData[0].realName = main.realName;
         }
         
         return {
           ...main, // 展开
           inzUserDeviceList: allValues.tablesValue[0].tableData,
           // 只有当有金豆记录数据时才添加到提交数据中
           inzUserPayLogList: hasPayLogData ? payLogData : [],
         }
       }
     //校验所有一对一子表表单
     function validateSubForm(allValues){
         return new Promise((resolve,reject)=>{
             // 检查金豆记录表单是否有数据
             const payLogData = inzUserPayLogForm.value.getFormData();
             const hasPayLogData = payLogData && payLogData.length > 0 && 
                                 (payLogData[0].content || payLogData[0].goldenBean || payLogData[0].type);
             
             // 如果没有填写金豆记录相关数据，则跳过验证直接返回
             if (!hasPayLogData) {
                 resolve(allValues);
                 return;
             }
             
             // 有金豆记录数据，执行验证
             Promise.all([
                  inzUserPayLogForm.value.validateForm(1),
             ]).then(() => {
                 resolve(allValues)
             }).catch(e => {
                 if (e.error === VALIDATE_FAILED) {
                     // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                     activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
                 } else {
                     console.error(e)
                 }
             })
         })
     }
    //表单提交事件
    async function requestAddOrEdit(values) {
      //提交表单
      await saveOrUpdate(values, true);
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
