<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    destroyOnClose
    :title="modalTitle"
    :width="600"
    @ok="handleSubmit"
  >
    <div class="join-class-container">
      <!-- 用户信息显示 -->
      <a-alert
        :message="`为用户 ${selectedUser?.realName || selectedUser?.username || '未知用户'} 选择要加入的班级`"
        type="info"
        show-icon
        style="margin-bottom: 16px"
      />

      <a-form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 选择班级 -->
        <a-form-item
          label="选择班级"
          name="classId"
          :rules="[{ required: true, message: '请选择要加入的班级' }]"
        >
          <a-select
            v-model:value="formState.classId"
            placeholder="请选择班级"
            :options="classOptions"
            show-search
            :filter-option="filterClassOption"
            allowClear
            @change="handleClassChange"
            :loading="loading"
          >
            <template #option="{ value, label, coachName, currentStudents, description }">
              <div class="class-option">
                <div class="class-name">{{ label }}</div>
                <div class="class-details">
                  <span class="coach-name">教练: {{ coachName }}</span>
                  <span class="student-count">学生数: {{ currentStudents || 0 }}</span>
                </div>
                <div class="class-description" v-if="description">{{ description }}</div>
              </div>
            </template>
          </a-select>
        </a-form-item>

        <!-- 加入备注 -->
        <a-form-item label="加入备注" name="remark">
          <a-textarea
            v-model:value="formState.remark"
            placeholder="请输入加入班级的备注信息（可选）"
            :rows="3"
            :maxlength="200"
            show-count
          />
        </a-form-item>

        <!-- 选中班级信息展示 -->
        <a-form-item v-if="selectedClass" label="班级信息">
          <div class="selected-class-info">
            <div class="info-item">
              <span class="label">班级名称:</span>
              <span class="value">{{ selectedClass.className }}</span>
            </div>
            <div class="info-item">
              <span class="label">教练姓名:</span>
              <span class="value">{{ selectedClass.coachName }}</span>
            </div>
            <div class="info-item">
              <span class="label">当前学生:</span>
              <span class="value">{{ selectedClass.currentStudents || 0 }} 人</span>
            </div>
            <div class="info-item" v-if="selectedClass.description">
              <span class="label">班级描述:</span>
              <span class="value">{{ selectedClass.description }}</span>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import { joinUserToClass, getClassList } from '../InzUserFront.api';

// 组件事件
const emit = defineEmits(['register', 'success']);

// 响应式数据
const formRef = ref();
const loading = ref(false);
const selectedUser = ref<any>(null);
const classOptions = ref<any[]>([]);
const selectedClass = ref<any>(null);

// 表单状态
const formState = reactive({
  classId: '',
  remark: ''
});

// 模态框标题
const modalTitle = computed(() => {
  return `加入班级 - ${selectedUser.value?.realName || selectedUser.value?.username || '用户'}`;
});

// 注册模态框
const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
  selectedUser.value = data.record;
  
  // 重置表单
  formState.classId = '';
  formState.remark = '';
  selectedClass.value = null;
  
  // 加载班级列表
  await loadClassList();
});

// 加载班级列表
const loadClassList = async () => {
  loading.value = true;
  try {
    const res = await getClassList({ pageSize: 9999 }); // 获取所有班级
    if (res && res.records) {
      classOptions.value = res.records.map((classItem: any) => ({
        label: classItem.className || '未知班级',
        value: classItem.id,
        coachName: classItem.coachName || '未知教练',
        currentStudents: classItem.currentStudents || 0,
        description: classItem.description || '',
        ...classItem
      }));
    }
  } catch (error) {
    console.error('获取班级列表失败:', error);
    message.error('获取班级列表失败');
  } finally {
    loading.value = false;
  }
};

// 班级搜索过滤
const filterClassOption = (input: string, option: any) => {
  const searchText = input.toLowerCase();
  return (
    option.label.toLowerCase().includes(searchText) ||
    option.coachName.toLowerCase().includes(searchText)
  );
};

// 处理班级选择变化
const handleClassChange = (value: string) => {
  selectedClass.value = classOptions.value.find(item => item.value === value);
  console.log('选择班级:', selectedClass.value);
};

// 表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();
    
    setModalProps({ confirmLoading: true });
    
    // 调用加入班级API
    const params = {
      userId: selectedUser.value.id,
      classId: formState.classId,
      remark: formState.remark
    };
    
    await joinUserToClass(params);
    
    message.success('用户加入班级成功');
    closeModal();
    emit('success');
    
  } catch (error) {
    console.error('加入班级失败:', error);
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    message.error('加入班级失败: ' + (error.message || '未知错误'));
  } finally {
    setModalProps({ confirmLoading: false });
  }
};
</script>

<style lang="less" scoped>
.join-class-container {
  padding: 16px 0;
}

.class-option {
  padding: 8px 0;
  
  .class-name {
    font-size: 14px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }
  
  .class-details {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 4px;
    
    .coach-name, .student-count {
      font-size: 12px;
      color: #8c8c8c;
    }
  }
  
  .class-description {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
  }
}

.selected-class-info {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 6px;
  
  .info-item {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      width: 80px;
      color: #666;
      font-size: 14px;
    }
    
    .value {
      flex: 1;
      color: #262626;
      font-size: 14px;
    }
  }
}

:deep(.ant-select-dropdown) {
  .ant-select-item-option-content {
    padding: 0;
  }
}
</style>
