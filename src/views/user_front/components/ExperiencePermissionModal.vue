<template>
  <BasicModal @register="registerModal" @ok="handleSubmit" :width="600" title="开启词书体验权限"
    :titleStyle="{ color: '#1890ff', fontSize: '18px' }" :bodyStyle="{ padding: '24px' }"
    :confirmLoading="confirmLoading">
    <div class="experience-permission-container">
      <a-spin :spinning="loading">
        <!-- 用户信息显示 -->
        <a-alert v-if="selectedUser" type="info"
          :message="`为用户 ${selectedUser.realName} (${selectedUser.phone}) 开通体验权限`" class="mb-4" show-icon />

        <!-- 表单区域 -->
        <a-form :model="formState" layout="vertical" ref="formRef">
          <!-- 教育分类选择 -->
          <a-form-item label="教育分类" name="educationId" :rules="[{ required: true, message: '请选择教育分类' }]">
            <a-select v-model:value="formState.educationId" placeholder="请选择教育分类" :options="educationOptions"
              @change="handleEducationChange" allowClear :loading="educationLoading" />
          </a-form-item>

          <!-- 权限类型选择 -->
          <a-form-item label="权限类型" name="permissionType" :rules="[{ required: true, message: '请选择权限类型' }]">
            <a-radio-group v-model:value="formState.permissionType" @change="handlePermissionTypeChange">
              <a-radio value="trial">试用权限</a-radio>
              <a-radio value="annual" :disabled="!canGrantAnnual">全年权限</a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 试用时长配置 (仅试用权限显示) -->
          <div v-if="formState.permissionType === 'trial'">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="剩余可配置的体验时长" name="duration" :rules="[{ required: true, message: '请输入试用时长' }]">
                  <a-input-number v-model:value="formState.duration" :min="1" :max="getMaxTrialDuration" :step="1"
                    placeholder="请输入试用时长" style="width: 100%" @change="handleDurationChange" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="时间单位" name="durationUnit" :rules="[{ required: true, message: '请选择时间单位' }]">
                  <a-select v-model:value="formState.durationUnit" placeholder="请选择时间单位"
                    @change="handleDurationUnitChange" :disabled="isTrialLimitExceeded">
                    <a-select-option value="days">天</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- 试用时长限制提示 -->
            <a-alert v-if="getRemainingTrialDays <= 0" type="error" message="您的免费试用额度已用完，请选择年度权限" class="mb-4"
              show-icon />
            <a-alert v-else-if="isTrialLimitExceeded" type="warning"
              :message="`剩余免费试用天数：${getRemainingTrialDays} 天，超出部分需选择年度权限`" class="mb-4" show-icon />
          </div>

          <!-- 费用显示 -->
          <a-alert v-if="formState.permissionType === 'trial' && isWithinFreeLimit" type="success" message="免费试用，无需金豆"
            class="mb-4" show-icon />
          <a-alert v-else-if="costInfo.cost > 0" type="info" :message="`预计费用：${costInfo.cost} 金豆`" class="mb-4"
            show-icon />

          <!-- 权限验证结果 -->
          <a-alert v-if="validationResult" :type="validationResult.canGrant ? 'success' : 'warning'"
            :message="validationResult.message" class="mb-4" show-icon />

          <!-- 用户试用状态 -->
          <a-alert v-if="trialStatus && formState.permissionType === 'trial'"
            :type="trialStatus.hasActiveTrial ? 'warning' : 'info'" :message="trialStatusMessage" class="mb-4"
            show-icon />

          <!-- 全年权限日期区间选择 -->
          <div v-if="formState.permissionType === 'annual'">
            <a-form-item label="权限有效期" name="dateRange" :rules="[{ required: true, message: '请选择权限有效期' }]">
              <a-range-picker v-model:value="formState.dateRange" show-time format="YYYY-MM-DD HH:mm:ss"
                placeholder="['开始时间', '结束时间']" style="width: 100%" @change="handleDateRangeChange"
                :disabled-date="disabledDate" />
            </a-form-item>
          </div>

          <!-- 调试信息 -->
          <a-alert v-if="formState.educationId" type="info"
            :message="`已选择教育分类: ${getEducationName(formState.educationId)}, 试用天数: ${formState.duration}`" class="mb-4"
            show-icon />

          <!-- 错误提示 -->
          <a-alert v-if="errorMsg" type="error" :message="errorMsg" class="mb-4" show-icon />
        </a-form>
      </a-spin>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { getEducationList, getConfig, getUserRemainingDays, grantTrial } from '../InzUserFront.api';
import { message } from 'ant-design-vue';
import { useUserStore } from '/@/store/modules/user';
import dayjs, { Dayjs } from 'dayjs';

// 响应式数据定义
const loading = ref(false);
const confirmLoading = ref(false);
const educationLoading = ref(false);
const errorMsg = ref('');
const educationOptions = ref<any[]>([]);
const selectedUser = ref<any>(null);
const validationResult = ref<any>(null);
const trialStatus = ref<any>(null);
const formRef = ref();
const userStore = useUserStore();
const defaultDays = ref(8); // 默认天数，将从配置中动态获取
const maxTrialDays = ref(8); // 最大试用天数限制，将从配置中动态获取
const userUsedTrialDays = ref(0); // 用户已使用的试用天数
const userRemainingDays = ref(8); // 用户剩余试用天数（从API直接获取）

// 表单状态
const formState = reactive({
  educationId: '',
  permissionType: 'trial',
  duration: 8, // 将在配置加载后动态设置，默认8天
  durationUnit: 'days',
  dateRange: [] as [Dayjs, Dayjs] | [],
});

// 费用信息
const costInfo = reactive({
  cost: 0,
});

// 计算属性
const getMaxTrialDuration = computed(() => {
  // 试用权限的最大时长就是剩余的免费试用天数
  return getRemainingTrialDays.value;
});

// 检查是否超过试用时长限制
const isTrialLimitExceeded = computed(() => {
  if (formState.permissionType !== 'trial') return false;
  return formState.duration > getRemainingTrialDays.value;
});

// 计算剩余试用天数（优先使用用户数据中的字段）
const getRemainingTrialDays = computed(() => {
  // 优先使用用户数据中的 trialRemainingDays 字段
  if (selectedUser.value?.trialRemainingDays !== undefined) {
    return selectedUser.value.trialRemainingDays;
  }
  // 否则使用API获取的值
  return userRemainingDays.value;
});

// 检查是否在免费试用范围内
const isWithinFreeLimit = computed(() => {
  if (formState.permissionType !== 'trial') return false;
  return formState.duration <= getRemainingTrialDays.value;
});

// 获取教育分类名称
const getEducationName = (educationId: string) => {
  const education = educationOptions.value.find((item) => item.value === educationId);
  return education?.label || educationId;
};

// 检查是否可以开通全年权限（需要合伙人及以上权限）
const canGrantAnnual = computed(() => {
  const userInfo = userStore.getUserInfo;
  // 这里需要根据实际的权限字段来判断
  return userInfo?.userType === 'partner' || userInfo?.userType === 'admin';
});

const trialStatusMessage = computed(() => {
  if (!trialStatus.value) return '';

  if (trialStatus.value.hasActiveTrial) {
    return `用户已有活跃试用权限，到期时间：${trialStatus.value.expiryDate}`;
  } else {
    return '用户当前无活跃试用权限，可以开通新的试用';
  }
});

// 注册模态框
const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
  selectedUser.value = data.record;

  // 直接从用户数据中获取剩余体验天数
  if (data.record.trialRemainingDays !== undefined) {
    userRemainingDays.value = data.record.trialRemainingDays;
    console.log(`用户 ${data.record.realName} 剩余体验天数: ${data.record.trialRemainingDays}`);
  } else {
    // 如果用户数据中没有该字段，则调用API获取
    await loadTrialStatus();
  }

  // 先重置表单，使用用户的剩余天数
  resetForm();

  // 然后加载配置（但不覆盖duration值）
  await loadDefaultConfig();

  // 最后加载教育数据
  loadEducationData();

  // 强制更新表单值，确保显示剩余可配置天数
  nextTick(() => {
    // 使用剩余试用天数作为默认值
    const remainingDays = getRemainingTrialDays.value;
    formState.duration = remainingDays > 0 ? remainingDays : 0;
    console.log('最终设置表单值 - 用户剩余天数:', remainingDays, 'formState.duration:', formState.duration);
  });
});

const emit = defineEmits(['success']);

// 重置表单
const resetForm = () => {
  formState.educationId = '';
  formState.permissionType = 'trial';

  // 使用剩余试用天数作为默认值，而不是总配置天数
  const remainingDays = getRemainingTrialDays.value;
  formState.duration = remainingDays > 0 ? remainingDays : 0;

  console.log('重置表单 - defaultDays:', defaultDays.value, 'maxTrialDays:', maxTrialDays.value, 'userUsedTrialDays:', userUsedTrialDays.value, 'remainingDays:', remainingDays, 'formState.duration:', formState.duration);

  formState.durationUnit = 'days';
  formState.dateRange = [];
  costInfo.cost = 0;
  errorMsg.value = '';
  validationResult.value = null;
  // 注意：不要重置 trialStatus，因为它包含了用户的试用状态信息

  nextTick(() => {
    formRef.value?.clearValidate();
  });
};

// 加载教育分类数据
const loadEducationData = async () => {
  educationLoading.value = true;
  try {
    const res = await getEducationList({});
    if (res && res.records) {
      educationOptions.value = res.records.map((item: any) => ({
        label: item.name,
        value: item.id,
        goldenBean: item.goldenBean || 0,
        ...item,
      }));
    }
  } catch (error) {
    console.error('获取教育分类失败:', error);
    message.error('获取教育分类失败');
  } finally {
    educationLoading.value = false;
  }
};

// 加载默认配置
const loadDefaultConfig = async () => {
  try {
    const res = await getConfig({ code: 'FREE_MEMBERSHIP_DAYS' });
    if (res && res.success && res.result) {
      const configValue = parseInt(res.result.value || '8');
      console.log('获取到的配置值:', configValue);
      defaultDays.value = configValue;
      maxTrialDays.value = configValue; // 设置最大试用天数限制

      // 不要直接设置formState.duration，应该使用用户的剩余天数
      // formState.duration = configValue; // 注释掉这行，避免覆盖用户剩余天数
      console.log('配置加载完成，maxTrialDays:', maxTrialDays.value, '用户剩余天数:', getRemainingTrialDays.value);
    } else {
      console.warn('配置获取失败或配置为空，使用默认值8天');
      defaultDays.value = 8;
      maxTrialDays.value = 8;
      // formState.duration = 8; // 注释掉这行，避免覆盖用户剩余天数
    }
  } catch (error) {
    console.error('获取默认配置失败:', error);
    // 使用默认值8天
    defaultDays.value = 8;
    maxTrialDays.value = 8;
    // formState.duration = 8; // 注释掉这行，避免覆盖用户剩余天数
  }
};

// 加载用户剩余试用天数
const loadTrialStatus = async () => {
  if (!selectedUser.value?.id) {
    console.warn('用户ID不存在，无法获取剩余试用天数');
    return;
  }

  try {
    console.log(`开始获取用户 ${selectedUser.value.realname} (ID: ${selectedUser.value.id}) 的剩余试用天数`);
    
    // 调用API获取用户剩余试用天数
    const res = await getUserRemainingDays({ userId: selectedUser.value.id });
    
    console.log('API返回结果:', res);

    if (res && res.success && typeof res.result === 'number') {
      // 直接获取剩余天数
      const remainingDays = res.result;
      userRemainingDays.value = remainingDays;

      // 计算已使用天数 = 总配置天数 - 剩余天数
      userUsedTrialDays.value = Math.max(0, maxTrialDays.value - remainingDays);

      console.log(`✅ 成功获取用户 ${selectedUser.value.realname} 剩余试用天数: ${remainingDays}`);
      console.log(`计算得出已使用天数: ${userUsedTrialDays.value}`);

      // 更新试用状态信息
      trialStatus.value = {
        hasActiveTrial: remainingDays > 0,
        remainingDays: remainingDays,
        usedTrialDays: userUsedTrialDays.value,
        totalTrialDays: maxTrialDays.value
      };
    } else {
      console.error('❌ API返回数据格式错误:', res);
      message.error('获取用户剩余试用天数失败：数据格式错误');
      // 设置为0而不是默认值，明确表示获取失败
      userRemainingDays.value = 0;
      userUsedTrialDays.value = maxTrialDays.value;
    }
  } catch (error) {
    console.error('❌ 获取用户剩余试用天数API调用失败:', error);
    message.error(`获取用户剩余试用天数失败: ${error.message || '网络错误'}`);
    // 设置为0而不是默认值，明确表示获取失败
    userRemainingDays.value = 0;
    userUsedTrialDays.value = maxTrialDays.value;
  }
};

// 教育分类变更处理
const handleEducationChange = () => {
  validateAndCalculateCost();
};

// 权限类型变更处理
const handlePermissionTypeChange = () => {
  if (formState.permissionType === 'trial') {
    // 切换到试用权限时，确保时长不超过剩余免费天数
    formState.durationUnit = 'days';
    const remainingDays = getRemainingTrialDays.value;
    if (formState.duration > remainingDays) {
      formState.duration = remainingDays;
    }
    formState.dateRange = []; // 清空日期范围
  } else {
    // 切换到年度权限时，设置默认日期范围
    const now = dayjs();
    formState.dateRange = [now, now.add(1, 'year')];
  }
  validateAndCalculateCost();
};

// 试用时长变更处理
const handleDurationChange = () => {
  const remainingDays = getRemainingTrialDays.value;

  // 如果超过剩余免费试用天数，自动切换到年度权限
  if (formState.duration > remainingDays) {
    message.warning(`剩余免费试用天数仅 ${remainingDays} 天，自动切换到年度权限`);
    formState.permissionType = 'annual';
    // 设置默认的年度权限日期范围
    const now = dayjs();
    formState.dateRange = [now, now.add(1, 'year')];
  }
  validateAndCalculateCost();
};

// 时间单位变更处理
const handleDurationUnitChange = () => {
  // 试用权限只能选择天数，且不能超过剩余免费天数
  if (formState.permissionType === 'trial') {
    formState.durationUnit = 'days';
    const remainingDays = getRemainingTrialDays.value;
    if (formState.duration > remainingDays) {
      formState.duration = remainingDays;
    }
  }
  validateAndCalculateCost();
};

// 日期范围变更处理
const handleDateRangeChange = () => {
  validateAndCalculateCost();
};

// 禁用过去的日期
const disabledDate = (current: Dayjs) => {
  return current && current < dayjs().startOf('day');
};

// 验证权限并计算费用 - 简化版本
const validateAndCalculateCost = async () => {
  if (!formState.educationId || !selectedUser.value?.id) {
    costInfo.cost = 0;
    validationResult.value = null;
    return;
  }

  try {
    // 使用本地计算费用
    const education = educationOptions.value.find((item) => item.value === formState.educationId);
    if (education) {
      const baseCost = education.goldenBean || 0;

      // 根据权限类型和时间长度计算费用
      if (formState.permissionType === 'trial') {
        // 试用权限：在免费范围内无需金豆
        const remainingDays = getRemainingTrialDays.value;
        if (formState.duration <= remainingDays) {
          costInfo.cost = 0; // 免费试用
        } else {
          // 超出免费范围，自动切换到年度权限
          formState.permissionType = 'annual';
          const now = dayjs();
          formState.dateRange = [now, now.add(1, 'year')];
          costInfo.cost = baseCost; // 年度费用就是golden_bean的值
        }
      } else {
        // 年度权限：费用为教育分类的golden_bean值
        costInfo.cost = baseCost;
      }
    }

    // 设置验证结果
    validationResult.value = {
      canGrant: true,
      message: '可以开通权限',
    };
  } catch (error) {
    console.error('计算费用失败:', error);
    validationResult.value = {
      canGrant: false,
      message: '计算费用失败，请重试',
    };
  }
};

// 表单提交
const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    // 年度权限需要验证日期范围
    if (formState.permissionType === 'annual') {
      if (!formState.dateRange || formState.dateRange.length !== 2) {
        errorMsg.value = '请选择权限有效期';
        return;
      }
    }

    // 权限验证
    if (!validationResult.value || !validationResult.value.canGrant) {
      errorMsg.value = validationResult.value?.message || '权限验证失败，无法开通';
      return;
    }

    confirmLoading.value = true;
    setModalProps({ confirmLoading: true });

    // 调用真实的API接口
    const apiParams = {
      userId: selectedUser.value.id,
      educationId: formState.educationId,
      duration: formState.duration,
      durationUnit: formState.durationUnit,
      permissionType: formState.permissionType,
    };

    // 如果是年度权限，添加日期范围
    if (formState.permissionType === 'annual') {
      apiParams.startDate = formState.dateRange[0].format('YYYY-MM-DD HH:mm:ss');
      apiParams.endDate = formState.dateRange[1].format('YYYY-MM-DD HH:mm:ss');
    }

    const response = await grantTrial(apiParams);

    if (response.success) {
      const permissionTypeText = formState.permissionType === 'trial' ? '试用' : '年度';
      const educationName = getEducationName(formState.educationId);

      let durationText = '';
      let costText = '';

      if (formState.permissionType === 'trial') {
        durationText = `${formState.duration} 天`;
        costText = costInfo.cost === 0 ? '免费' : `${costInfo.cost} 金豆`;
      } else {
        const startDate = formState.dateRange[0].format('YYYY-MM-DD');
        const endDate = formState.dateRange[1].format('YYYY-MM-DD');
        durationText = `${startDate} 至 ${endDate}`;
        costText = `${costInfo.cost} 金豆`;
      }

      message.success(`${permissionTypeText}权限开通成功！
用户：${selectedUser.value.realName}
教育分类：${educationName}
费用：${costText}
时长：${durationText}`);

      emit('success');
      closeModal();
    } else {
      throw new Error(response.message || '开通失败');
    }
  } catch (error: any) {
    console.error('开通权限失败:', error);
    errorMsg.value = error?.message || '开通失败，请重试';
  } finally {
    confirmLoading.value = false;
    setModalProps({ confirmLoading: false });
  }
};

// 监听表单变化，自动验证和计算费用
watch(
  () => [formState.educationId, formState.permissionType, formState.duration, formState.durationUnit, formState.dateRange],
  () => {
    if (formState.educationId && selectedUser.value?.id) {
      validateAndCalculateCost();
    }
  },
  { immediate: false }
);
</script>

<style lang="less" scoped>
.experience-permission-container {
  .mb-4 {
    margin-bottom: 16px;
  }
}
</style>
