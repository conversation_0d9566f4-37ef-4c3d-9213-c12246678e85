import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '/@/utils/common/renderUtils';
import { JVxeColumn, JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
import { ref } from 'vue';
import dayjs from 'dayjs';
import { getGradeList, getUserFrontList } from '@/views/user_front/InzUserFront.api';
import { defHttp } from '@/utils/http/axios';
import { useUserStore } from '/@/store/modules/user';

// 角色权限控制工具函数

// 判断是否是代理商角色
export function isAgentRole(roleCode: string): boolean {
  return roleCode && ['chuang', 'channel', 'area_partner', 'city_partner', 'province_partner'].includes(roleCode);
}

// 获取角色显示名称
export function getRoleDisplayName(roleCode: string): string {
  const roleMap = {
    chuang: '创总',
    channel: '渠道商',
    area_partner: '区域合伙人',
    city_partner: '城市合伙人',
    province_partner: '省级合伙人',
    user: '普通用户',
    vip: 'VIP用户',
    normal: '普通用户',
    coach: '教练',
  };
  return roleMap[roleCode] || roleCode || '未知角色';
}

// 获取当前用户信息
export function getCurrentUserInfo() {
  const userStore = useUserStore();
  return {
    roleCode: userStore.getRoleList?.[0] || '',
    username: userStore.getUserInfo?.username || '',
    isAgent: isAgentRole(userStore.getRoleList?.[0] || ''),
  };
}

// 加载用户列表

// Function commented out as it's not used
/* 
function convertCodesToChinese(codes: string[]): string {
  if (!codes || codes.length === 0) return '';

  const names = [];
  for (let i = 0; i < codes.length; i++) {
    const code = codes[i];
    const data = getDataByCode(i === 0 ? '86' : codes[i - 1]); // 第一级用86作为父级
    const area = data.find((item) => item.value === code);
    if (area) {
      names.push(area.label);
    }
  }
  return names.join('/');
}
*/

async function loadUserList() {
  try {
    const res = await getUserFrontList({ pageSize: 9999 }); // 获取所有用户
    console.log('res', res);
    userList.value = res || [];
  } catch (e) {
    console.error('获取用户列表失败', e);
  }
}

async function loadGradeList() {
  try {
    const res = await getGradeList({ pageSize: 9999 }); // 获取所有年级
    gradeList.value = res || [];
    console.log('gradeList', gradeList);
  } catch (e) {
    console.error('获取年级列表失败', e);
  }
}

let _chapterDict = [];

defHttp
  .get({ url: '/grade/inzGrade/listAll' })
  .then((res) => {
    _chapterDict = res || [];
  })
  .catch((e) => console.error('图书数据加载失败:', e));

// 初始化加载
loadUserList();
loadGradeList();
const userList = ref<any[]>([]);
const gradeList = ref<any[]>([]);
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '真实姓名',
    align: 'center',
    dataIndex: 'realName',
  },
  {
    title: '年级',
    align: 'center',
    dataIndex: 'grade',
    customRender: ({ record }) => {
      const grade = gradeList.value.find((grade) => grade.id === record.grade);
      console.log(record.grade);
      return grade ? grade.text : '无';
    },
  },
  {
    title: '手机号',
    align: 'center',
    dataIndex: 'phone',
  },
  {
    title: '用户角色',
    align: 'center',
    dataIndex: 'role',
    width: 120,
    customRender: ({ text }) => {
      return getRoleDisplayName(text);
    },
  },
  {
    title: '上级用户',
    align: 'center',
    dataIndex: 'parentId',
    width: 120,
    customRender: ({ text }) => {
      if (!text) return '-';
      const parentUser = userList.value.find((user) => user.id === text);
      return parentUser ? parentUser.realName : '未知用户';
    },
  },
  // {
  //   title: '密码',
  //   align:"center",
  //   dataIndex: 'password'
  // },
  {
    title: '地址',
    align: 'center',
    dataIndex: 'address',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    customRender: ({ text }) => {
      if (text == 1) {
        return '正常';
      } else {
        return '停用';
      }
    },
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
  {
    title: '头像',
    align: 'center',
    dataIndex: 'avatar',
    customRender: render.renderImage,
  },
  {
    title: '最后一次进入系统的时间',
    align: 'center',
    dataIndex: 'lastUseAt',
  },
  {
    title: '拥有金豆',
    align: 'center',
    dataIndex: 'goldenBean',
  },
  {
    title: '会员到期时间',
    align: 'center',
    dataIndex: 'vipTime',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '用户身份',
    align: 'center',
    dataIndex: 'role',
    customRender: ({ text }) => {
      if (text == 'normal') {
        return '普通用户';
      } else if (text == 'coach') {
        return '教练';
      } else if (text == 'vip') {
        return 'VIP用户';
      } else if (text == 'chuang') {
        return '创总';
      } else if (text == 'channel') {
        return '渠道';
      } else if (text == 'area_partner') {
        return '区/县合伙人';
      } else if (text == 'city_partner') {
        return '市级合伙人';
      } else if (text == 'province_partner') {
        return '省级合伙人';
      }
    },
  },
  {
    title: '是否试学',
    align: 'center',
    dataIndex: 'tryStudy',
    customRender: ({ text }) => {
      if (text > 0) {
        return '已试学';
      } else {
        return '未试学';
      }
    },
  },
  {
    title: '是否为正式用户',
    align: 'center',
    dataIndex: 'formalUser',
    customRender: ({ text }) => {
      if (text > 0) {
        return '正式用户';
      } else {
        return '非正式用户';
      }
    },
  },
  {
    title: '推荐人',
    align: 'center',
    dataIndex: 'parentId',
    customRender: ({ record }) => {
      const parentUser = userList.value.find((user) => user.id === record.parentId);
      return parentUser ? parentUser.realName : '无';
    },
  },
  {
    title: '推荐人身份',
    align: 'center',
    dataIndex: 'parentType',
    customRender: ({ record }) => {
      const parentUser = userList.value.find((user) => user.id === record.parentId);
      return parentUser ? parentUser.role : '无';
    },
  },
  {
    title: '推荐人手机号',
    align: 'center',
    dataIndex: 'parentPhone',
    customRender: ({ record }) => {
      const parentUser = userList.value.find((user) => user.id === record.parentId);
      return parentUser ? parentUser.phone : '无';
    },
  },
  {
    title: '注册时间',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '剩余体验天数',
    align: 'center',
    dataIndex: 'trialRemainingDays',
    width: 120,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 },
      ],
    },
  },
  {
    label: '是否试学',
    field: 'tryStudy',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未试学', value: 0 },
        { label: '已试学', value: 1 },
      ],
    },
  },
  {
    label: '是否为正式用户',
    field: 'formalUser',
    component: 'Select',
    componentProps: {
      options: [
        { label: '正式用户', value: 1 },
        { label: '非正式用户', value: 0 },
      ],
    },
  },
  {
    label: '用户角色',
    field: 'role',
    component: 'Select',
    componentProps: {
      options: [
        { label: '普通用户', value: 'normal' },
        { label: 'VIP用户', value: 'vip' },
        { label: '教练', value: 'coach' },
        { label: '创总', value: 'chuang' },
        { label: '渠道商', value: 'channel' },
        { label: '区域合伙人', value: 'area_partner' },
        { label: '城市合伙人', value: 'city_partner' },
        { label: '省级合伙人', value: 'province_partner' },
      ],
    },
    colProps: { span: 8 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
  },
  {
    label: '年级',
    field: 'grade',
    component: 'Select',
    componentProps: () => {
      return {
        options: gradeList.value.map((item) => ({
          label: item.text,
          value: item.id,
        })),
      };
    },
  },
  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
  },
  /*{
    label: '密码',
    field: 'password',
    component: 'Input',
  },*/
  {
    label: '用户角色',
    field: 'role',
    component: 'Select',
    componentProps: {
      options: [
        { label: '普通用户', value: 'normal' },
        { label: 'VIP用户', value: 'vip' },
        { label: '教练', value: 'coach' },
        { label: '创总', value: 'chuang' },
        { label: '渠道商', value: 'channel' },
        { label: '区域合伙人', value: 'area_partner' },
        { label: '城市合伙人', value: 'city_partner' },
        { label: '省级合伙人', value: 'province_partner' },
      ],
    },
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '停用', value: 0 },
      ],
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '头像',
    field: 'avatar',
    component: 'JImageUpload',
  },
  {
    label: '会员到期时间',
    field: 'vipTime',
    component: 'DatePicker',
    componentProps: {
      //日期格式化，页面上显示的值
      format: 'YYYY-MM-DD',
      //返回值格式化（绑定值的格式）
      valueFormat: 'YYYY-MM-DD',
      //是否显示今天按钮
      showToday: true,
      showTime: {
        defaultValue: dayjs('00:00:00', 'HH:mm:ss'),
      },
      //不可选择日期
      disabledDate: (currentDate) => {
        const date = dayjs(currentDate).format('YYYY-MM-DD');
        const nowDate = dayjs(new Date()).format('YYYY-MM-DD');
        //当天不可选择
        if (date == nowDate) {
          return true;
        }
        return false;
      },
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表列表数据
export const inzUserDeviceColumns: BasicColumn[] = [
  {
    title: '用户id',
    align: 'center',
    dataIndex: 'userId',
  },
  {
    title: 'token',
    align: 'center',
    dataIndex: 'token',
  },
  {
    title: '过期时间戳',
    align: 'center',
    dataIndex: 'exprie',
  },
  {
    title: '设备名称',
    align: 'center',
    dataIndex: 'deviceName',
  },
  {
    title: '系统版本名称',
    align: 'center',
    dataIndex: 'systemName',
  },
  {
    title: '浏览器版本名称',
    align: 'center',
    dataIndex: 'browerName',
  },
  {
    title: '登录时间',
    align: 'center',
    dataIndex: 'loginTime',
  },
];
//子表列表数据
export const inzUserPayLogColumns: BasicColumn[] = [
  {
    title: '接受金豆姓名',
    align: 'center',
    dataIndex: 'userId',
    width: 120,
    customRender: ({ record }) => {
      const user = userList.value.find((user) => user.id === record.userId);
      return user ? user.realName : '未知用户';
    },
  },
  {
    title: '接受金豆手机号',
    align: 'center',
    dataIndex: 'userId',
    width: 130,
    customRender: ({ record }) => {
      const user = userList.value.find((user) => user.id === record.userId);
      return user ? user.phone || '无手机号' : '无手机号';
    },
  },
  {
    title: '接受金豆数量',
    align: 'center',
    dataIndex: 'goldenBean',
    width: 120,
    customRender: ({ text, record }) => {
      // 只显示增加的金豆数量
      if (record.type == 1 || record.type == 3) {
        // 增加或退款
        return `+${text}`;
      }
      return '-';
    },
  },
  {
    title: '消耗金豆数量',
    align: 'center',
    dataIndex: 'goldenBean',
    width: 120,
    customRender: ({ text, record }) => {
      // 只显示减少的金豆数量
      if (record.type == 0 || record.type == 2) {
        // 扣除或消费
        return `-${text}`;
      }
      return '-';
    },
  },
  {
    title: '剩余金豆数量',
    align: 'center',
    dataIndex: 'afterBalance',
    width: 120,
    customRender: ({ text }) => {
      return text || '0';
    },
  },
  {
    title: '接受金豆时间',
    align: 'center',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '操作描述',
    align: 'center',
    dataIndex: 'content',
    width: 200,
  },
  {
    title: '操作类型',
    align: 'center',
    dataIndex: 'type',
    width: 100,
    customRender: ({ text }) => {
      const typeMap = {
        0: '扣除',
        1: '增加',
        2: '消费',
        3: '退款',
      };
      return typeMap[text] || '未知';
    },
  },
];
export const inzUserPayLogFormSchema: FormSchema[] = [
  {
    label: '用户id',
    field: 'userId',
    component: 'Input',
  },
  {
    label: '用户姓名',
    field: 'realName',
    component: 'Input',
    componentProps: {
      disabled: true,
      placeholder: '系统自动填充用户姓名',
    },
  },
  {
    label: '描述',
    field: 'content',
    component: 'Input',
  },
  {
    label: '金豆数量',
    field: 'goldenBean',
    component: 'InputNumber',
  },
  {
    label: '类型 1增加 0减少',
    field: 'type',
    component: 'Select',
    componentProps: {
      options: [
        { label: '增加', value: 1 },
        { label: '减少', value: 0 },
      ],
    },
    // 移除了 dynamicRules，使字段变为非必填
  },
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表表格配置
export const inzUserDeviceJVxeColumns: JVxeColumn[] = [
  {
    title: '用户id',
    key: 'userId',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: 'token',
    key: 'token',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '过期时间戳',
    key: 'exprie',
    type: JVxeTypes.datetime,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '设备名称',
    key: 'deviceName',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '系统版本名称',
    key: 'systemName',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '浏览器版本名称',
    key: 'browerName',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '登录时间',
    key: 'loginTime',
    type: JVxeTypes.datetime,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  realName: { title: '真实姓名', order: 0, view: 'text', type: 'string' },
  grade: { title: '年级', order: 1, view: 'text', type: 'string' },
  phone: { title: '手机号', order: 2, view: 'text', type: 'string' },
  password: { title: '密码', order: 3, view: 'text', type: 'string' },
  address: { title: '地址', order: 4, view: 'text', type: 'string' },
  name: { title: '名字', order: 5, view: 'text', type: 'string' },
  oaNickname: { title: '公众号昵称', order: 6, view: 'text', type: 'string' },
  oaOpenid: { title: '公众号 OpenId', order: 7, view: 'text', type: 'string' },
  mpOpenid: { title: '小程序OpenId', order: 8, view: 'text', type: 'string' },
  ewOpenid: { title: '企业微信 OpenId', order: 9, view: 'text', type: 'string' },
  unionId: { title: '联合 Id', order: 10, view: 'text', type: 'string' },
  role: { title: '角色', order: 11, view: 'text', type: 'string' },
  status: { title: '状态 (1正常 0停用)', order: 12, view: 'text', type: 'string' },
  remark: { title: '备注', order: 13, view: 'text', type: 'string' },
  avatar: { title: '头像', order: 14, view: 'text', type: 'string' },
  lastUseAt: { title: '最后一次进入系统的时间', order: 15, view: 'text', type: 'string' },
  vipTime: { title: '会员时间', order: 16, view: 'date', type: 'string' },
  //子表高级查询
  inzUserDevice: {
    title: '用户登录设备',
    view: 'table',
    fields: {
      userId: { title: '用户id', order: 0, view: 'text', type: 'string' },
      token: { title: 'token', order: 1, view: 'text', type: 'string' },
      exprie: { title: '过期时间戳', order: 2, view: 'datetime', type: 'string' },
      deviceName: { title: '设备名称', order: 3, view: 'text', type: 'string' },
      systemName: { title: '系统版本名称', order: 4, view: 'text', type: 'string' },
      browerName: { title: '浏览器版本名称', order: 5, view: 'text', type: 'string' },
      loginTime: { title: '登录时间', order: 6, view: 'datetime', type: 'string' },
    },
  },
  inzUserPayLog: {
    title: '用户金豆记录',
    view: 'table',
    fields: {
      userId: { title: '用户id', order: 0, view: 'text', type: 'string' },
      realName: { title: '用户姓名', order: 1, view: 'text', type: 'string' },
      content: { title: '描述', order: 2, view: 'text', type: 'string' },
      goldenBean: { title: '金豆数量', order: 3, view: 'number', type: 'number' },
      type: { title: '类型 1增加 0减少', order: 4, view: 'text', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
