<template>
  <div>
    <!--    &lt;!&ndash; 页面头部 &ndash;&gt;-->
    <!--    <div class="page-header">-->
    <!--      <h2>用户管理</h2>-->
    <!--      <div class="user-info">-->
    <!--        <a-tag color="blue">-->
    <!--          {{ currentUserInfo.role || '用户' }}-->
    <!--        </a-tag>-->
    <!--        <span class="user-name">{{ currentUserInfo.username || '用户' }}</span>-->
    <!--        <span class="user-phone">{{ currentUserInfo.phone || '' }}</span>-->
    <!--        <a-button-->
    <!--          v-if="isDevelopment"-->
    <!--          size="small"-->
    <!--          @click="showUserStatus">-->
    <!--          调试状态-->
    <!--        </a-button>-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- 权限说明 -->
    <a-alert
      v-if="currentUserInfo.permissionDescription"
      :message="currentUserInfo.permissionDescription"
      type="info"
      show-icon
      style="margin-bottom: 16px"
    />

    <!-- 权限提示：后端会处理权限控制，前端不需要预先判断 -->
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :expandedRowKeys="expandedRowKeys" @expand="handleExpand">
      <!-- 内嵌table区域 begin -->
      <template #expandedRowRender="{ record }">
        <a-tabs tabPosition="top">
          <a-tab-pane tab="用户登录设备" key="inzUserDevice" forceRender>
            <inzUserDeviceSubTable :id="expandedRowKeys[0]" />
          </a-tab-pane>
          <a-tab-pane tab="用户金豆记录" key="inzUserPayLog" forceRender>
            <inzUserPayLogSubTable :id="expandedRowKeys[0]" />
          </a-tab-pane>
        </a-tabs>
      </template>
      <!-- 内嵌table区域 end -->
      <!--插槽:table标题-->
      <template #tableTitle>
        <!--          <a-button type="primary" v-auth="'user_front:inz_user_front:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>-->
        <!--          <a-button  type="primary" v-auth="'user_front:inz_user_front:exportXls'"  preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>-->
        <!--          <j-upload-button  type="primary" v-auth="'user_front:inz_user_front:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>-->
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchAssignBeans">
                <Icon icon="ant-design:gift-outlined" />
                批量分配金豆
              </a-menu-item>
              <a-menu-item key="2" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                批量删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'user_front:inz_user_front:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 开通单词书按钮 - 仅对代理商角色显示 -->
        <a-button v-if="isAgent" type="primary" style="margin-left: 8px" @click="openBookModal" preIcon="ant-design:book-outlined">
          开通单词书
        </a-button>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }"></template>
    </BasicTable>
    <!-- 表单区域 -->
    <InzUserFrontModal @register="registerModal" @success="handleSuccess"></InzUserFrontModal>
    <AssignBeansModal @register="registerAssignModal" @success="handleSuccess" />
    <UpdatePasswordModal @register="registerPasswordModal" @success="handleSuccess" />
    <OpenBookModal @register="registerOpenBookModal" @success="handleSuccess" />
    <ExperiencePermissionModal @register="registerExperienceModal" @success="handleSuccess" />
    <JoinClassModal @register="registerJoinClassModal" @success="handleSuccess" />
    <AddToClassModal @register="registerAddToClassModal" @success="handleSuccess" />

  </div>
</template>

<style scoped>
  .role-info-container {
    margin-bottom: 16px;
  }

  .role-info-content {
    line-height: 1.6;
  }

  .role-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .role-details > div {
    margin-bottom: 4px;
  }

  .debug-info {
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
  }
</style>

<script lang="ts" name="user_front-inzUserFront" setup>
  import { computed, onMounted, reactive, ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import InzUserFrontModal from './components/InzUserFrontModal.vue';
  import InzUserDeviceSubTable from './subTables/InzUserDeviceSubTable.vue';
  import InzUserPayLogSubTable from './subTables/InzUserPayLogSubTable.vue';
  import { columns, searchFormSchema, superQuerySchema } from './InzUserFront.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from './InzUserFront.api';
  import { useUserStore } from '/@/store/modules/user';
  import { useUserStatusStore } from '/@/store/modules/userStatus';
  import { message } from 'ant-design-vue';
  import UpdatePasswordModal from './components/UpdatePasswordModel.vue';
  import AssignBeansModal from './components/AssignBeansModal.vue';
  import OpenBookModal from './components/OpenBookModal.vue';
  import ExperiencePermissionModal from './components/ExperiencePermissionModal.vue';
  import JoinClassModal from './components/JoinClassModal.vue';
  import AddToClassModal from './components/AddToClassModal.vue';


  const queryParam = reactive<any>({});
  // 展开key
  const expandedRowKeys = ref<any[]>([]);
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerAssignModal, { openModal: openAssignModal }] = useModal();
  const [registerPasswordModal, { openModal: openPasswordModal }] = useModal();
  const [registerOpenBookModal, { openModal: openOpenBookModal }] = useModal();
  const [registerExperienceModal, { openModal: openExperienceModal }] = useModal();
  const [registerJoinClassModal, { openModal: openJoinClassModal }] = useModal();
  const [registerAddToClassModal, { openModal: openAddToClassModal }] = useModal();

  const userStore = useUserStore();
  const userStatusStore = useUserStatusStore();

  // 开发环境标识
  const isDevelopment = computed(() => process.env.NODE_ENV === 'development');

  // 当前用户信息（从后端接口获取）
  const currentUserInfo = ref({
    username: '',
    phone: '',
    role: '',
    permissionDescription: '',
  });

  // 获取当前用户信息
  const getCurrentUser = () => {
    const userInfo = userStore.getUserInfo;
    const roleList = userStore.getRoleList;

    // 详细打印用户信息用于调试
    console.log('=== 用户信息调试 ===');
    console.log('完整用户信息:', userInfo);
    console.log('用户角色 (role):', userInfo?.role);
    console.log('角色列表 (roleList):', roleList);
    console.log('用户权限 (authorities):', userInfo?.authorities);
    console.log('用户名 (username):', userInfo?.username);
    console.log('真实姓名 (realName):', userInfo?.realName);
    console.log('手机号 (phone):', userInfo?.phone);
    console.log('用户ID (id):', userInfo?.id);
    console.log('所有字段:', Object.keys(userInfo || {}));
    console.log('==================');

    // 尝试从多个地方获取角色信息
    let role = userInfo?.role || roleList?.[0] || userInfo?.authorities?.[0] || userInfo?.roleCode || userInfo?.roleName || '';

    // 特殊处理：如果用户名是admin，且没有角色信息，默认设为管理员
    if (!role && userInfo?.username === 'admin') {
      console.log('🔧 检测到admin用户但无角色信息，默认设置为管理员');
      role = 'admin';
    }

    return {
      phone: userInfo?.phone,
      role: role,
      realName: userInfo?.realName || userInfo?.realname,
      id: userInfo?.id,
      username: userInfo?.username,
    };
  };

  // 判断是否是代理商角色（与后端保持一致）
  const isAgentRole = (roleCode) => {
    return roleCode && ['chuang', 'channel', 'area_partner', 'city_partner', 'province_partner'].includes(roleCode);
  };

  // 获取角色显示名称
  const getRoleDisplayName = (roleCode) => {
    const roleMap = {
      chuang: '创总',
      channel: '渠道商',
      area_partner: '区域合伙人',
      city_partner: '城市合伙人',
      province_partner: '省级合伙人',
      admin: '管理员',
      administrator: '管理员',
      super_admin: '超级管理员',
      system_admin: '系统管理员',
      root: '超级管理员',
      user: '普通用户',
      normal: '普通用户',
      vip: 'VIP用户',
      coach: '教练',
    };

    // 如果包含admin关键字，也认为是管理员
    if (roleCode?.toLowerCase().includes('admin')) {
      return '管理员';
    }

    return roleMap[roleCode] || roleCode || '未知角色';
  };

  // 判断是否是管理员角色
  const isAdminRole = (role) => {
    const adminRoles = ['admin', '管理员', 'administrator', 'super_admin', 'system_admin', 'root'];
    return adminRoles.includes(role) || role?.toLowerCase().includes('admin');
  };

  // 判断用户角色类型
  const getUserRoleType = (role) => {
    console.log('判断角色类型，输入角色:', role);

    if (isAdminRole(role)) {
      console.log('✅ 识别为管理员角色');
      return 'ADMIN';
    } else if (isAgentRole(role)) {
      console.log('✅ 识别为代理商角色:', getRoleDisplayName(role));
      return 'AGENT';
    } else {
      console.log('❌ 识别为普通用户或未知角色，角色值:', role);
      return 'USER';
    }
  };

  // 计算当前用户角色类型
  const currentUser = computed(() => getCurrentUser());
  const roleType = computed(() => getUserRoleType(currentUser.value.role));
  const isAgent = computed(() => roleType.value === 'AGENT');
  const isAdmin = computed(() => roleType.value === 'ADMIN');

  // 调试相关变量
  const debugInfo = ref({
    visible: false,
    content: '',
  });
  const isDev = ref(process.env.NODE_ENV === 'development');

  // 直接使用后端list接口（已包含完整权限控制）
  const dynamicListApi = async (params) => {
    console.log('=== 调用后端list接口 ===');
    console.log('请求参数:', params);

    try {
      // 直接调用后端接口，后端会处理所有权限控制
      const result = await list(params);
      console.log('✅ 接口调用成功，数据量:', result.records?.length || 0);

      // 更新用户信息显示（基于后端日志推断）
      updateCurrentUserInfo(result);

      return result;
    } catch (error) {
      console.log('❌ 接口调用失败:', error.message);

      // 从错误信息中提取用户信息（如果有的话）
      updateCurrentUserInfoFromError(error);

      // 重新抛出错误，让表格组件处理
      throw error;
    }
  };

  // 更新当前用户信息显示
  const updateCurrentUserInfo = (result) => {
    // 直接使用后端返回的用户信息
    if (result.userInfo) {
      currentUserInfo.value = {
        username: result.userInfo.username || '',
        phone: result.userInfo.phone || '',
        role: result.userInfo.roleDescription || '',
        permissionDescription: `当前显示 ${result.records?.length || 0} 条用户数据`,
      };

      // 如果是代理商用户，显示金豆统计信息
      if (result.goldenBeanStats) {
        currentUserInfo.value.permissionDescription += ` | 金豆统计: 总计${result.goldenBeanStats.totalGoldenBean}, 已用${result.goldenBeanStats.usedGoldenBean}, 剩余${result.goldenBeanStats.remainingGoldenBean}`;
      }
    } else {
      // 使用默认的推断逻辑
      const dataCount = result.records?.length || 0;
      currentUserInfo.value.permissionDescription = `当前显示 ${dataCount} 条用户数据`;
    }
  };

  // 从错误信息中提取用户信息
  const updateCurrentUserInfoFromError = (error) => {
    if (error.message?.includes('权限')) {
      currentUserInfo.value.permissionDescription = '您没有权限查看用户列表，请联系管理员';
    }
  };
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: computed(() => {
        if (roleType.value === 'ADMIN') return '用户管理 - 全部用户';
        if (roleType.value === 'AGENT') return '用户管理 - 我的推荐用户';
        return '用户列表';
      }),
      api: dynamicListApi,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: ['tryStudy', 'formalUser', 'status'], // 将这些字段映射为数字类型
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '用户表',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 展开事件
   * */
  function handleExpand(expanded, record) {
    expandedRowKeys.value = [];
    if (expanded === true) {
      expandedRowKeys.value.push(record.id);
    }
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  async function batchAssignBeans() {
    if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
      message.warning('请至少选择一位用户');
      return;
    }

    openAssignModal(true, {
      title: `批量调整金豆 (共${selectedRowKeys.value.length}人)`,
      isBatch: true,
      userIds: [...selectedRowKeys.value], // 确保传递数组副本
    });
  }

  function handleUpdatePassword(record) {
    openPasswordModal(true, {
      record,
    });
  }

  // 单个用户金豆编辑
  function handleAssignBeans(record) {
    openAssignModal(true, {
      title: `调整金豆 - ${record.realName || record.username}`,
      isBatch: false,
      userIds: [record.id],
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调 - 增强版本
   */
  function handleSuccess() {
    console.log('操作成功，正在刷新数据...');
    // 清空选中的行
    selectedRowKeys.value = [];
    // 刷新表格数据
    reload();
    // 如果有展开的行，也刷新展开行的数据
    if (expandedRowKeys.value.length > 0) {
      console.log('刷新展开行数据...');
      // 触发展开行数据刷新
      setTimeout(() => {
        // 通过重新设置展开行来触发子表数据刷新
        const currentExpanded = [...expandedRowKeys.value];
        expandedRowKeys.value = [];
        setTimeout(() => {
          expandedRowKeys.value = currentExpanded;
        }, 100);
      }, 500);
    }
    console.log('数据刷新完成');
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'user_front:inz_user_front:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    console.log('生成下拉菜单，包含添加到班级选项'); // 添加调试日志
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '划分金豆',
        onClick: () => handleAssignBeans(record),
        auth: 'user_front:inz_user_front:assignBeans',
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'user_front:inz_user_front:delete',
      },
      {
        label: '修改密码',
        onClick: () => handleUpdatePassword(record),
        auth: 'user_front:inz_user_front:updatePassword',
      },
      {
        label: '开启体验权限',
        onClick: () => handleOpenExperiencePermission(record),
        // auth: 'user_front:inz_user_front:openExperience', // 暂时注释权限，方便测试
      },
      {
        label: '加入班级',
        onClick: () => handleJoinClass(record),
        auth: 'user_front:inz_user_front:joinClass',
      },
      {
        label: '添加到班级',
        onClick: () => handleAddToClass(record),
        // auth: 'inz_coach_class:inz_coach_class:addUser', // 暂时注释权限，方便测试
      },
      {
        label: '退单',
        popConfirm: {
          title: `确认要为用户 ${record.realName} 进行退单操作吗？`,
          confirm: () => handleRefundOrder(record),
          placement: 'topLeft',
        },
        /*auth: 'user_front:inz_user_front:refund',*/
      },
    ];
  }

  // 开通单词书
  function openBookModal() {
    openOpenBookModal(true, {});
  }

  // 开启体验权限
  function handleOpenExperiencePermission(record) {
    console.log('开启体验权限被点击', record);
    openExperienceModal(true, {
      record,
    });
  }

  // 加入班级
  function handleJoinClass(record) {
    console.log('加入班级被点击', record);
    openJoinClassModal(true, {
      record,
    });
  }

  // 添加到班级
  function handleAddToClass(record) {
    console.log('添加到班级被点击', record);
    openAddToClassModal(true, {
      record,
    });
  }

  // 退单处理
  async function handleRefundOrder(record) {
    console.log('退单被点击', record);

    try {
      const { refundAnnualPermission } = await import('./InzUserFront.api');

      console.log('开始调用退单API，用户ID:', record.id);
      const res = await refundAnnualPermission({
        userId: record.id
      });

      console.log('退单API返回结果:', res);

      // 检查响应结构
      if (res && res.success === true) {
        console.log('退单成功');
        message.success(`用户 ${record.realName} 退单成功`);
        handleSuccess(); // 刷新列表
      } else {
        console.log('退单失败，原因:', res?.message || '未知原因');
        message.error(res?.message || '退单失败');
      }
    } catch (error: any) {
      console.error('退单API调用异常:', error);
      // 避免重复显示错误信息
      if (error?.response?.data?.message) {
        message.error(error.response.data.message);
      } else if (error?.message) {
        message.error(error.message);
      } else {
        message.error('退单失败，请重试');
      }
    }
  }

  // 显示用户状态调试信息
  function showUserStatus() {
    const user = getCurrentUser();
    const role = getUserRoleType(user.role);

    const statusInfo = {
      当前用户: user,
      角色类型: role,
      角色显示名: getRoleDisplayName(user.role),
      是否管理员: role === 'ADMIN',
      是否代理商: role === 'AGENT',
      有管理权限: role === 'ADMIN' || role === 'AGENT',
      用户Store信息: userStore.getUserInfo,
      角色列表: userStore.getRoleList,
    };

    console.log('=== 用户状态调试信息 ===');
    console.log(statusInfo);
    console.log('==================');

    // 使用ant-design-vue的Modal显示
    import('ant-design-vue').then(({ Modal }) => {
      Modal.info({
        title: '用户状态调试信息',
        content: JSON.stringify(statusInfo, null, 2),
        width: 600,
        okText: '确定',
      });
    });
  }

  // 获取当前用户信息
  const fetchCurrentUserInfo = () => {
    // 从userStore获取基本用户信息
    const userInfo = userStore.getUserInfo;

    if (userInfo) {
      currentUserInfo.value = {
        username: userInfo.username || '',
        phone: userInfo.phone || '',
        role: getRoleDisplayName(userInfo.role || userStore.getRoleList?.[0] || ''),
        permissionDescription: '',
      };
    }
  };

  // 页面初始化时获取用户状态
  onMounted(() => {
    // 获取当前用户信息
    fetchCurrentUserInfo();
    console.log('页面初始化完成，使用后端list接口处理权限控制');
  });
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .user-name {
    color: #666;
    font-size: 14px;
  }
</style>
