import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/user_front/inzUserFront/list',
  save = '/user_front/inzUserFront/add',
  edit = '/user_front/inzUserFront/edit',
  deleteOne = '/user_front/inzUserFront/delete',
  deleteBatch = '/user_front/inzUserFront/deleteBatch',
  importExcel = '/user_front/inzUserFront/importExcel',
  exportXls = '/user_front/inzUserFront/exportXls',
  inzUserDeviceList = '/user_front/inzUserFront/queryInzUserDeviceByMainId',
  inzUserPayLogList = '/user_front/inzUserFront/queryInzUserPayLogByMainId',
  listAll = '/user_front/inzUserFront/listAll',
  gradeList = '/grade/inzGrade/listAll',
  openBook = '/user_front/inzUserFront/openBook',
  getEducationList = '/education/inzEducation/rootList',
  getConfig = '/config/inzConfig/getConfig',
  grantTrial = '/user_front/permission/grantTrial',
  refundAnnualPermission = '/user_front/refund/refundAnnualPermission',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzUserDevice = Api.inzUserDeviceList;
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzUserPayLog = Api.inzUserPayLogList;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp
        .delete(
          {
            url: Api.deleteBatch,
            data: params,
          },
          { joinParamsToUrl: true }
        )
        .then(() => {
          handleSuccess();
        });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};
/**
 * 子表列表接口
 * @param params
 */
export const inzUserDeviceList = (params) =>
  defHttp.get(
    {
      url: Api.inzUserDeviceList,
      params,
    },
    { isTransformResponse: false }
  );
/**
 * 子表列表接口
 * @param params
 */
export const inzUserPayLogList = (params) =>
  defHttp.get(
    {
      url: Api.inzUserPayLogList,
      params,
    },
    { isTransformResponse: false }
  );
export const assignBeans = (params) => {
  return defHttp.post({
    url: '/user_front/inzUserFront/assignBeans',
    params,
  });
};

// 正确的金豆记录查询接口
export const queryInzUserPayLogByMainId = (params) =>
  defHttp.get(
    {
      url: '/user_front/inzUserFront/queryInzUserPayLogByMainId',
      params,
    },
    { isTransformResponse: false }  // 添加这个参数
  );

// 获取用户列表（使用原生fetch，支持权限控制）
export const getUserListWithPermission = async (pageNo = 1, pageSize = 10, searchParams = {}) => {
  try {
    const params = new URLSearchParams({
      pageNo: pageNo.toString(),
      pageSize: pageSize.toString(),
      ...searchParams,
    });

    const response = await fetch(`/jeecg-boot/user_front/inzUserFront/list?${params}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Access-Token': localStorage.getItem('Access-Token') || '',
      },
    });

    const result = await response.json();

    if (result.success) {
      return result.result;
    } else {
      throw new Error(result.message || '获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    throw error;
  }
};

// 调试用户角色信息接口（保留原有的defHttp方式作为备用）
export const debugUserRole = () =>
  defHttp.get({
    url: '/user_front/inzUserFront/debugUserRole',
  });

// 代理商获取直接推荐用户接口
export const getDirectReferrals = (params) =>
  defHttp.get({
    url: '/user_front/inzUserFront/getDirectReferrals',
    params,
  });

// 统一的角色权限用户列表接口
export const getUsersByRole = (params) =>
  defHttp.get({
    url: '/user_front/inzUserFront/getUsersByRole',
    params,
  });
// 修改密码接口
export const updatePassword = (params: { userId: string; newPassword: string }) => {
  return defHttp.put({ url: '/user_front/inzUserFront/updatePassword', params });
};

export const getUserFrontList = (params) => {
  return defHttp.get({ url: Api.listAll, params });
};
export const getGradeList = (params) => {
  return defHttp.get({ url: Api.gradeList, params });
};

// 获取教育阶段列表
export const getEducationList = (params) => {
  return defHttp.get({ url: Api.getEducationList, params });
};

// 获取配置信息
export const getConfig = (params: { code: string }) => {
  return defHttp.get({ url: Api.getConfig, params });
};

// 获取用户剩余试用天数
export const getUserRemainingDays = (params: { userId: string }) => {
  return defHttp.get({
    url: '/user_front/permission/getUserRemainingDays',
    params,
  });
};

// 获取班级列表
export const getClassList = (params) => {
  return defHttp.get({
    url: '/inz_coach_class/inzCoachClass/list',
    params
  });
};

// 用户加入班级
export const joinUserToClass = (params: { userId: string, classId: string, remark?: string }) => {
  return defHttp.post({
    url: '/user_front/inzUserFront/joinClass',
    params
  });
};

// 添加用户到班级（教练操作）
export const addUserToClass = (params: { userId: string, classId: string }) => {
  return defHttp.post({
    url: '/inz_coach_class/inzCoachClass/addUserToClass',
    params
  });
};

// 获取我的班级列表（当前用户创建的班级）
export const getMyClassList = (params?: any) => {
  return defHttp.get({
    url: '/inz_coach_class/inzCoachClass/getClassList',
    params
  });
};

// 为用户开通单词书
export const openBookForUser = (params) => {
  return defHttp.post({ url: Api.openBook, params });
};

// 开通体验权限
export const grantTrial = (params: {
  userId: string;
  educationId: string;
  duration: number;
  durationUnit: string;
  permissionType: string;
}) => {
  return defHttp.post({ url: Api.grantTrial, params });
};

// 退单年度权限
export const refundAnnualPermission = (params: {
  userId: string;
}) => {
  return defHttp.post({
    url: Api.refundAnnualPermission,
    params,
    // 禁用自动错误提示，避免重复显示
    errorMessageMode: 'none'
  });
};
