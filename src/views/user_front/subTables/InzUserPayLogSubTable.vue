<template>
  <div>
      <!--引用表格-->
     <BasicTable
       bordered
       size="small"
       :loading="loading"
       rowKey="id"
       :canResize="false"
       :columns="inzUserPayLogColumns"
       :dataSource="dataSource"
       :pagination="false"
       :scroll="{ x: 1000 }"
     >
        <!--字段回显插槽-->
        <template v-slot:bodyCell="{ column, record, index, text }">
        </template>
      </BasicTable>
    </div>
</template>

<script lang="ts" setup>
  import {ref,watchEffect} from 'vue';
  import {BasicTable} from '/@/components/Table';
  import {inzUserPayLogColumns} from '../InzUserFront.data';
  import {inzUserPayLogList, queryInzUserPayLogByMainId} from '../InzUserFront.api';
  import { downloadFile } from '/@/utils/common/renderUtils';

  const props = defineProps({
    id: {
       type: String,
       default: '',
     },
  })

  const loading = ref(false);
  const dataSource = ref([]);

  watchEffect(() => {
      props.id && loadData(props.id);
   });

   function loadData(id) {
         dataSource.value = []
         loading.value = true
         console.log('开始加载金豆记录，用户ID:', id);

         queryInzUserPayLogByMainId({ id }).then((res) => {
           console.log('金豆记录API完整返回:', res);
           if (res.success) {
             // 根据后端接口，返回的是 Result<IPage<InzUserPayLog>>
             // res.result 是 IPage 对象，包含 records 和 total
             const pageData = res.result;
             console.log('分页数据:', pageData);

             if (pageData && pageData.records) {
               dataSource.value = pageData.records;
               console.log('金豆记录加载成功，共', pageData.records.length, '条记录');
               console.log('记录详情:', pageData.records);
             } else {
               console.warn('金豆记录数据格式异常:', pageData);
               dataSource.value = [];
             }
           } else {
             console.warn('金豆记录API调用失败:', res);
             dataSource.value = [];
           }
         }).catch((error) => {
           console.error('获取金豆记录失败:', error);
           dataSource.value = [];
         }).finally(() => {
           loading.value = false
         })
    }
</script>
