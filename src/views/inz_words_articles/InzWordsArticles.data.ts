import { BasicColumn, FormSchema } from '/@/components/Table';
import { defHttp } from '@/utils/http/axios';

let bookDict = [];
let chapterDict = [];

defHttp
  .get({ url: '/books/inzWordBooks/listAll' })
  .then((res) => {
    bookDict = res || [];
  })
  .catch((e) => console.error('图书数据加载失败:', e));
defHttp
  .get({ url: '/books/inzWordBooks/chapterListAll' })
  .then((res) => {
    chapterDict = res || [];
  })
  .catch((e) => console.error('图书数据加载失败:', e));

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '词书',
    align: 'center',
    dataIndex: 'bookId',
    customRender: ({ text }) => {
      const book = bookDict.find((item) => item.id === text);
      return book?.name || text;
    },
  },
  {
    title: '章节',
    align: 'center',
    dataIndex: 'chapterId',
    customRender: ({ text }) => {
      const chatper = chapterDict.find((item) => item.id === text);
      return chatper?.name || text;
    },
  },
  {
    title: '单词ID列表，以逗号分隔',
    align: 'center',
    dataIndex: 'wordIds',
  },
  {
    title: '单词列表，以逗号分隔',
    align: 'center',
    dataIndex: 'words',
  },
  {
    title: '短文内容',
    align: 'center',
    dataIndex: 'content',
  },
  {
    title: '中文释义',
    align: 'center',
    dataIndex: 'chineseTranslation',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '词书ID',
    field: 'bookId',
    component: 'Input',
  },
  {
    label: '章节ID',
    field: 'chapterId',
    component: 'Input',
  },
  {
    label: '单词ID列表，以逗号分隔',
    field: 'wordIds',
    component: 'InputTextArea',
  },
  {
    label: '单词列表，以逗号分隔',
    field: 'words',
    component: 'InputTextArea',
  },
  {
    label: '短文内容',
    field: 'content',
    component: 'InputTextArea',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  bookId: { title: '词书ID', order: 0, view: 'text', type: 'string' },
  chapterId: { title: '章节ID', order: 1, view: 'text', type: 'string' },
  wordIds: { title: '单词ID列表，以逗号分隔', order: 2, view: 'textarea', type: 'string' },
  words: { title: '单词列表，以逗号分隔', order: 3, view: 'textarea', type: 'string' },
  content: { title: '短文内容', order: 4, view: 'textarea', type: 'string' },
  title: { title: '短文标题', order: 5, view: 'text', type: 'string' },
  groupIndex: { title: '组序号（同一章节内的组序号）', order: 6, view: 'number', type: 'number' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
