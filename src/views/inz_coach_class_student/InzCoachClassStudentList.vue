<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button
          type="primary"
          v-auth="'inz_coach_class_student:inz_coach_class_student:addServiceUser'"
          @click="handleAddServiceUser"
          preIcon="ant-design:user-add-outlined"
          style="margin-left: 8px"
        >
          添加服务用户
        </a-button>
        <a-button
          type="primary"
          v-auth="'inz_coach_class_student:inz_coach_class_student:exportXls'"
          preIcon="ant-design:export-outlined"
          @click="onExportXls"
        >
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'inz_coach_class_student:inz_coach_class_student:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
        <a-button
          type="primary"
          @click="handleFeedback"
          preIcon="ant-design:message-outlined"
          style="margin-left: 8px"
        >
          学生反馈
        </a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }"></template>
    </BasicTable>
    <!-- 表单区域 -->
    <InzCoachClassStudentModal @register="registerModal" @success="handleSuccess"></InzCoachClassStudentModal>
    <!-- 添加服务用户模态框 -->
    <AddServiceUserModal @register="registerAddServiceUserModal" @success="handleSuccess"></AddServiceUserModal>
    <!-- 学生反馈模态框 -->
    <StudentFeedbackModal @register="registerFeedbackModal" @success="handleSuccess"></StudentFeedbackModal>
  </div>
</template>

<script lang="ts" name="inz_coach_class_student-inzCoachClassStudent" setup>
  import { reactive, ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import InzCoachClassStudentModal from './components/InzCoachClassStudentModal.vue';
  import AddServiceUserModal from './components/AddServiceUserModal.vue';
  import StudentFeedbackModal from './components/StudentFeedbackModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './InzCoachClassStudent.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from './InzCoachClassStudent.api';
  import { useUserStore } from '/@/store/modules/user';

  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerAddServiceUserModal, { openModal: openAddServiceUserModal }] = useModal();
  const [registerFeedbackModal, { openModal: openFeedbackModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '班级学生表',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '班级学生表',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 添加服务用户事件
   */
  function handleAddServiceUser() {
    openAddServiceUserModal(true, {});
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 学生反馈事件
   */
  function handleFeedback() {
    openFeedbackModal(true, {});
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'inz_coach_class_student:inz_coach_class_student:edit',
      },
      {
        label: '反馈',
        onClick: () => openFeedbackModal(true, { record }),
        // auth: 'inz_coach_class_student:inz_coach_class_student:feedback', // 暂时注释权限，方便测试
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'inz_coach_class_student:inz_coach_class_student:delete',
      },
    ];
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
