<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose title="学生反馈" :width="600" @ok="handleSubmit">
    <div class="student-feedback-container">
      <!-- 功能说明 -->
      <a-alert message="为选中的学生填写实时反馈信息" type="info" show-icon style="margin-bottom: 16px" />

      <a-form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 选择学生 -->
        <a-form-item label="选择学生" name="studentId" :rules="[{ required: true, message: '请选择要反馈的学生' }]">
          <a-select
            v-model:value="formState.studentId"
            placeholder="请输入学生姓名或电话搜索"
            show-search
            :filter-option="filterStudentOption"
            allowClear
            @change="handleStudentChange"
            :loading="studentLoading"
            :fieldNames="{ label: 'label', value: 'value' }"
          >
            <a-select-option
              v-for="option in studentOptions"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            >
              <div class="student-option">
                <div class="student-name">{{ option.studentName }}</div>
                <div class="student-details">
                  <span class="student-phone">电话: {{ option.studentPhone }}</span>
                  <span class="join-time">加入时间: {{ option.joinTime }}</span>
                </div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 选中学生信息展示 -->
        <a-form-item v-if="selectedStudent" label="学生信息">
          <div class="selected-student-info">
            <div class="info-item">
              <span class="label">姓名:</span>
              <span class="value">{{ selectedStudent.studentName }}</span>
            </div>
            <div class="info-item">
              <span class="label">电话:</span>
              <span class="value">{{ selectedStudent.studentPhone }}</span>
            </div>
            <div class="info-item">
              <span class="label">加入时间:</span>
              <span class="value">{{ selectedStudent.joinTime }}</span>
            </div>
            <div class="info-item" v-if="selectedStudent.feedback">
              <span class="label">当前反馈:</span>
              <span class="value">{{ selectedStudent.feedback }}</span>
            </div>
          </div>
        </a-form-item>

        <!-- 反馈内容 -->
        <a-form-item label="反馈内容" name="feedback" :rules="[{ required: true, message: '请输入反馈内容' }]">
          <a-textarea 
            v-model:value="formState.feedback" 
            placeholder="请输入对该学生的实时反馈信息" 
            :rows="6" 
            :maxlength="500" 
            show-count 
          />
        </a-form-item>

        <!-- 反馈时间 -->
        <a-form-item label="反馈时间" name="feedbackTime">
          <a-date-picker 
            v-model:value="formState.feedbackTime" 
            show-time 
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择反馈时间（默认当前时间）"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { message } from 'ant-design-vue';
  import { getClassStudent, updateStudentFeedback } from '../InzCoachClassStudent.api'; // 修改导入
  import { useUserStore } from '/@/store/modules/user'; // 新增导入
  import dayjs from 'dayjs';

  // 组件事件
  const emit = defineEmits(['register', 'success']);

  // 响应式数据
  const formRef = ref();
  const studentLoading = ref(false);
  const studentOptions = ref([]);
  const selectedStudent = ref(null);
  const userStore = useUserStore(); // 新增用户store
  
  // 表单数据
  const formState = reactive({
    studentId: '',
    feedback: '',
    feedbackTime: dayjs(),
  });

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 重置表单
    formRef.value?.resetFields();
    formState.studentId = '';
    formState.feedback = '';
    formState.feedbackTime = dayjs();
    selectedStudent.value = null;
    
    setModalProps({ confirmLoading: false });
    
    // 加载学生列表
    await loadStudentList();
    
    // 如果传入了学生记录，直接选中
    if (data?.record) {
      // 使用学生用户ID（用于提交反馈）
      formState.studentId = data.record.studentId;
      selectedStudent.value = {
        ...data.record,
        label: data.record.studentName,
        value: data.record.studentId,  // 使用studentId作为value
        recordId: data.record.id       // 保留记录ID
      };
      formState.feedback = data.record.feedback || '';

      console.log('预选学生记录:', data.record);
      console.log('设置的studentId（用于提交）:', formState.studentId);
      console.log('记录ID:', data.record.id);
    }
  });s

  // 加载学生列表 - 使用新接口
  async function loadStudentList() {
    try {
      studentLoading.value = true;

      // 获取当前用户ID
      const userInfo = userStore.getUserInfo;
      if (!userInfo?.id) {
        message.error('无法获取当前用户信息');
        return;
      }

      console.log('正在调用getClassStudent接口，用户ID:', userInfo.id);

      // 调用新的接口
      const res = await getClassStudent({ userId: userInfo.id });

      console.log('接口返回结果:', res);

      // 修复数据处理逻辑 - 检查多种可能的数据结构
      if (res && (res.success === true || res.success === undefined)) {
        let studentData = [];

        // 尝试多种数据结构
        if (res.result && Array.isArray(res.result)) {
          studentData = res.result;
        } else if (res.records && Array.isArray(res.records)) {
          studentData = res.records;
        } else if (Array.isArray(res)) {
          studentData = res;
        } else if (res.data && Array.isArray(res.data)) {
          studentData = res.data;
        }

        console.log('解析到的学生数据:', studentData);

        if (studentData.length > 0) {
          studentOptions.value = studentData.map(item => ({
            label: item.studentName || '未知学生',  // 显示学生姓名
            value: item.studentId,                 // 使用学生用户ID作为值（用于提交反馈）
            studentName: item.studentName || '未知学生',
            studentPhone: item.studentPhone || '无电话',
            joinTime: item.joinTime || '未知时间',
            feedback: item.feedback || '',
            studentId: item.studentId,             // 学生用户ID（重要：用于提交反馈）
            classId: item.classId,                 // 班级ID
            recordId: item.id,                     // 班级学生记录ID
            ...item
          }));

          console.log('处理后的学生选项:', studentOptions.value);
          message.success(`成功加载${studentData.length}个学生`);
        } else {
          studentOptions.value = [];
          message.warning('当前没有学生数据');
        }
      } else {
        console.error('接口返回失败:', res);
        message.error(res?.message || '获取学生列表失败');
      }
    } catch (error) {
      console.error('加载学生列表失败:', error);
      message.error(`加载学生列表失败: ${error.message || '未知错误'}`);
    } finally {
      studentLoading.value = false;
    }
  }

  // 优化学生选择过滤函数
  const filterStudentOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.studentName?.toLowerCase().includes(searchText) ||
      option.studentPhone?.includes(searchText) ||
      option.label?.toLowerCase().includes(searchText)
    );
  };

  // 学生选择变化
  function handleStudentChange(value: string) {
    console.log('学生选择变化，选中的value:', value);

    if (!value) {
      // 清空选择
      selectedStudent.value = null;
      formState.feedback = '';
      return;
    }

    const student = studentOptions.value.find(item => item.value === value);
    console.log('找到的学生信息:', student);

    selectedStudent.value = student;

    // 如果学生已有反馈，填充到表单中；否则清空
    formState.feedback = student?.feedback || '';
  }

  // 表单提交
  async function handleSubmit() {
    try {
      await formRef.value?.validate();
      setModalProps({ confirmLoading: true });

      const params = {
        id: formState.studentId,  // 这里的studentId实际是学生用户ID
        feedback: formState.feedback,
        feedbackTime: formState.feedbackTime.format('YYYY-MM-DD HH:mm:ss'),
      };

      console.log('提交反馈参数:', params);
      console.log('选中的学生信息:', selectedStudent.value);

      await updateStudentFeedback(params);
      message.success('反馈提交成功');
      closeModal();
      emit('success');
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('提交反馈失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  // 组件挂载时加载数据
  onMounted(() => {
    // 初始化时不加载，等模态框打开时再加载
  });
</script>

<style lang="less" scoped>
.student-feedback-container {
  .student-option {
    .student-name {
      font-weight: 500;
      color: #1890ff;
    }
    .student-details {
      font-size: 12px;
      color: #666;
      margin-top: 2px;
      .student-phone, .join-time {
        margin-right: 12px;
      }
    }
  }

  .selected-student-info {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 6px;
    .info-item {
      display: flex;
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        width: 80px;
        color: #666;
        flex-shrink: 0;
      }
      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }
}
</style>
