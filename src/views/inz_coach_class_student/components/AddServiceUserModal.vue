<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose title="添加学生到班级" :width="700" @ok="handleSubmit">
    <div class="add-service-user-container">
      <!-- 功能说明 -->
      <a-alert message="通过输入学生姓名和电话筛选用户，选择班级后将用户加入到班级中" type="info" show-icon style="margin-bottom: 16px" />

      <a-form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <!-- 选择班级 -->
        <a-form-item label="选择班级" name="classId" :rules="[{ required: true, message: '请选择班级' }]">
          <a-select
            v-model:value="formState.classId"
            placeholder="请选择要加入的班级"
            :options="classOptions"
            show-search
            :filter-option="filterClassOption"
            allowClear
            @change="handleClassChange"
            :loading="classLoading"
          >
            <template #option="{ value, label, coachName, currentStudents, description }">
              <div class="class-option">
                <div class="class-name">{{ label }}</div>
                <div class="class-details">
                  <span class="coach-name">教练: {{ coachName }}</span>
                  <span class="student-count">学生数: {{ currentStudents || 0 }}</span>
                </div>
                <div class="class-description" v-if="description">{{ description }}</div>
              </div>
            </template>
          </a-select>
        </a-form-item>

        <!-- 筛选用户 -->
        <a-form-item label="筛选用户" name="userId" :rules="[{ required: true, message: '请选择要添加的用户' }]">
          <a-select
            v-model:value="formState.userId"
            placeholder="请输入姓名或电话筛选用户"
            :options="userOptions"
            show-search
            :filter-option="filterUserOption"
            allowClear
            @search="handleUserSearch"
            @change="handleUserChange"
            :loading="userLoading"
            :not-found-content="userLoading ? '搜索中...' : '未找到匹配用户'"
          >
            <template #option="{ value, label, phone, role, realName }">
              <div class="user-option">
                <div class="user-name">{{ realName || label }}</div>
                <div class="user-details">
                  <span class="user-phone">电话: {{ phone }}</span>
                  <a-tag size="small" :color="getRoleColor(role)">
                    {{ getRoleDisplayName(role) }}
                  </a-tag>
                </div>
              </div>
            </template>
          </a-select>
        </a-form-item>

        <!-- 选中用户信息展示 -->
        <a-form-item v-if="selectedUser" label="选中用户">
          <div class="selected-user-info">
            <div class="info-item">
              <span class="label">姓名:</span>
              <span class="value">{{ selectedUser.realName || selectedUser.username }}</span>
            </div>
            <div class="info-item">
              <span class="label">电话:</span>
              <span class="value">{{ selectedUser.phone }}</span>
            </div>
            <div class="info-item">
              <span class="label">角色:</span>
              <a-tag size="small" :color="getRoleColor(selectedUser.role)">
                {{ getRoleDisplayName(selectedUser.role) }}
              </a-tag>
            </div>
          </div>
        </a-form-item>

        <!-- 加入备注 -->
        <a-form-item label="加入备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入加入班级的备注信息（可选）" :rows="3" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { onMounted, reactive, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { message } from 'ant-design-vue';
  import { addUserToClass, getCoachClassList, searchServiceUsers } from '../InzCoachClassStudent.api';
  import { getRoleDisplayName } from '/@/views/user_front/InzUserFront.data';

  // 组件事件
  const emit = defineEmits(['register', 'success']);

  // 响应式数据
  const formRef = ref();
  const classLoading = ref(false);
  const userLoading = ref(false);
  const classOptions = ref<any[]>([]);
  const userOptions = ref<any[]>([]);
  const selectedUser = ref<any>(null);
  const selectedClass = ref<any>(null);

  // 表单状态
  const formState = reactive({
    classId: '',
    userId: '',
    remark: '',
  });

  // 获取角色颜色
  const getRoleColor = (role: string) => {
    const roleColors = {
      创始人: 'purple',
      渠道商: 'blue',
      区域合伙人: 'green',
      城市合伙人: 'orange',
      省级合伙人: 'red',
      管理员: 'volcano',
      系统管理员: 'magenta',
      普通用户: 'default',
      VIP用户: 'gold',
    };
    return roleColors[role] || 'blue';
  };

  // 注册模态框
  const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
    // 重置表单状态
    formState.classId = '';
    formState.userId = '';
    formState.remark = '';
    selectedUser.value = null;
    selectedClass.value = null;
    userOptions.value = [];

    // 加载教练班级列表
    await loadCoachClassList();
  });

  // 加载教练班级列表
  const loadCoachClassList = async () => {
    classLoading.value = true;
    try {
      const res = await getCoachClassList(); // 获取当前教练管辖的班级
      if (res && res.records) {
        classOptions.value = res.records.map((classItem: any) => ({
          label: classItem.className || '未知班级',
          value: classItem.id,
          coachName: classItem.coachName || '未知教练',
          currentStudents: classItem.currentStudents || 0,
          description: classItem.description || '',
          ...classItem,
        }));
      }
    } catch (error) {
      console.error('获取班级列表失败:', error);
      message.error('获取班级列表失败');
    } finally {
      classLoading.value = false;
    }
  };

  // 班级搜索过滤
  const filterClassOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return option.label.toLowerCase().includes(searchText) || option.coachName.toLowerCase().includes(searchText);
  };

  // 处理班级选择变化
  const handleClassChange = (value: string) => {
    selectedClass.value = classOptions.value.find((item) => item.value === value);
    console.log('选择班级:', selectedClass.value);
  };

  // 用户搜索处理
  const handleUserSearch = async (searchText: string) => {
    if (!searchText || searchText.length < 2) {
      userOptions.value = [];
      return;
    }

    userLoading.value = true;
    try {
      const params = {
        searchText: searchText.trim(),
        classId: formState.classId, // 传递班级ID，后端可以过滤已在班级中的用户
      };

      const res = await searchServiceUsers(params);

      if (res && res.records) {
        userOptions.value = res.records.map((user: any) => ({
          label: user.realName || user.username || '未知用户',
          value: user.id,
          phone: user.phone || '无手机号',
          role: user.role || '普通用户',
          realName: user.realName || user.username,
          ...user,
        }));
      } else {
        userOptions.value = [];
      }
    } catch (error) {
      console.error('搜索用户失败:', error);
      userOptions.value = [];
    } finally {
      userLoading.value = false;
    }
  };

  // 用户搜索过滤
  const filterUserOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return option.label.toLowerCase().includes(searchText) || option.phone.toLowerCase().includes(searchText);
  };

  // 处理用户选择变化
  const handleUserChange = (value: string) => {
    selectedUser.value = userOptions.value.find((item) => item.value === value);
    console.log('选择用户:', selectedUser.value);
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      // 表单验证
      await formRef.value?.validate();

      if (!formState.classId) {
        message.warning('请选择班级');
        return;
      }

      if (!formState.userId) {
        message.warning('请选择要添加的用户');
        return;
      }

      setModalProps({ confirmLoading: true });

      // 调用添加用户到班级API
      const params = {
        classId: formState.classId,
        userId: formState.userId,
        remark: formState.remark,
      };

      await addUserToClass(params);

      message.success('成功将用户加入班级');
      closeModal();
      emit('success');
    } catch (error) {
      console.error('添加用户到班级失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('添加用户到班级失败: ' + (error.message || '未知错误'));
    } finally {
      setModalProps({ confirmLoading: false });
    }
  };

  // 组件挂载时加载班级列表
  onMounted(() => {
    loadCoachClassList();
  });
</script>

<style lang="less" scoped>
  .add-service-user-container {
    padding: 16px 0;
  }

  .class-option {
    padding: 8px 0;

    .class-name {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }

    .class-details {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 4px;

      .coach-name,
      .student-count {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    .class-description {
      font-size: 12px;
      color: #666;
      margin-top: 4px;
    }
  }

  .user-option {
    padding: 8px 0;

    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }

    .user-details {
      display: flex;
      align-items: center;
      gap: 8px;

      .user-phone {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .selected-user-info {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 6px;

    .info-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 60px;
        color: #666;
        font-size: 14px;
      }

      .value {
        flex: 1;
        color: #262626;
        font-size: 14px;
      }
    }
  }

  :deep(.ant-select-dropdown) {
    .ant-select-item-option-content {
      padding: 0;
    }
  }
</style>
