import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/inz_coach_class_student/inzCoachClassStudent/list',
  save='/inz_coach_class_student/inzCoachClassStudent/add',
  edit='/inz_coach_class_student/inzCoachClassStudent/edit',
  deleteOne = '/inz_coach_class_student/inzCoachClassStudent/delete',
  deleteBatch = '/inz_coach_class_student/inzCoachClassStudent/deleteBatch',
  importExcel = '/inz_coach_class_student/inzCoachClassStudent/importExcel',
  exportXls = '/inz_coach_class_student/inzCoachClassStudent/exportXls',
  searchServiceUsers = '/inz_coach_class_student/inzCoachClassStudent/searchServiceUsers',
  addUsersToClass = '/inz_coach_class_student/inzCoachClassStudent/addUsersToClass',
  updateFeedback = '/inz_coach_class_student/inzCoachClassStudent/updateFeedback',
  getClassStudent = '/inz_coach_class_student/inzCoachClassStudent/getClassStudent', // 新增接口
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 搜索服务用户（根据姓名和电话筛选）
 * @param params
 */
export const searchServiceUsers = (params: { searchText?: string, classId?: string }) => {
  return defHttp.get({url: Api.searchServiceUsers, params});
}

/**
 * 添加单个用户到班级
 * @param params
 */
export const addUserToClass = (params: { classId: string, userId: string, remark?: string }) => {
  return defHttp.post({url: Api.addUsersToClass, params});
}

/**
 * 获取教练管辖的班级列表
 * @param params
 */
export const getCoachClassList = (params?: any) => {
  return defHttp.get({url: '/inz_coach_class/inzCoachClass/getClassList', params});
}

/**
 * 更新学生反馈
 * @param params - id参数实际应该传入studentId（学生用户ID）
 */
export const updateStudentFeedback = (params: { id: string, feedback: string, feedbackTime?: string }) => {
  return defHttp.post({url: Api.updateFeedback, params});
}

/**
 * 获取教练班级下的学生列表
 * @param params
 */
export const getClassStudent = (params: { userId: string }) => {
  return defHttp.get({url: Api.getClassStudent, params});
}
