<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection" :expandedRowKeys="expandedRowKeys"  @expand="handleExpand">
      <!-- 内嵌table区域 begin -->
           <template #expandedRowRender="{record}">
             <a-tabs tabPosition="top">
               <a-tab-pane tab="词书课节管理" key="inzWordBookChapter" forceRender>
                  <inzWordBookChapterSubTable :id="expandedRowKeys[0]"/>
               </a-tab-pane>
             </a-tabs>
           </template>
     <!-- 内嵌table区域 end -->
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'books:inz_word_books:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'books:inz_word_books:exportXls'"  preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button  type="primary" v-auth="'books:inz_word_books:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-button type="primary" preIcon="ant-design:save-outlined" @click="handleSavePrompt" style="margin-right: 8px;"> 保存提示词</a-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button  v-auth="'books:inz_word_books:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <InzWordBooksModal @register="registerModal" @success="handleSuccess"></InzWordBooksModal>
    
    <!-- 保存提示词对话框 -->
    <a-modal v-model:visible="promptModalVisible" title="保存提示词" @ok="handlePromptSubmit" :confirmLoading="promptModalLoading">
      <a-form layout="vertical">
        <a-form-item label="提示词内容" required>
          <a-textarea v-model:value="promptText" :rows="6" placeholder="请输入提示词内容"></a-textarea>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="books-inzWordBooks" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage'
  import {useModal} from '/@/components/Modal';
  import InzWordBooksModal from './components/InzWordBooksModal.vue'
  import InzWordBookChapterSubTable from './subTables/InzWordBookChapterSubTable.vue'
  import {columns, searchFormSchema, superQuerySchema} from './InzWordBooks.data';
  import {list, deleteOne, batchDelete, getImportUrl, getExportUrl, savePrompt} from './InzWordBooks.api';
  import {downloadFile} from '/@/utils/common/renderUtils';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';
  
  const { createMessage } = useMessage();
  const queryParam = reactive<any>({});
   // 展开key
  const expandedRowKeys = ref<any[]>([]);
  //注册model
  const [registerModal, {openModal}] = useModal();
  const userStore = useUserStore();
  
  // 提示词相关状态
  const promptModalVisible = ref(false);
  const promptModalLoading = ref(false);
  const promptText = ref('');
  
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '词书表',
           api: list,
           columns,
           canResize:false,
           formConfig: {
                //labelWidth: 120,
                schemas: searchFormSchema,
                autoSubmitOnEnter:true,
                showAdvancedButton:true,
                fieldMapToNumber: [
                ],
                fieldMapToTime: [
                ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
           },
           beforeFetch: (params) => {
             return Object.assign(params, queryParam);
           },
        },
        exportConfig: {
            name:"词书表",
            url: getExportUrl,
            params: queryParam,
        },
        importConfig: {
            url: getImportUrl,
            success: handleSuccess
        },
    })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

   // 高级查询配置
   const superQueryConfig = reactive(superQuerySchema);

   /**
   * 高级查询事件
   */
   function handleSuperQuery(params) {
     Object.keys(params).map((k) => {
       queryParam[k] = params[k];
     });
     reload();
   }

   /**
     * 展开事件
     * */
   function handleExpand(expanded, record){
        expandedRowKeys.value=[];
        if (expanded === true) {
           expandedRowKeys.value.push(record.id)
        }
    }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value},handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'books:inz_word_books:edit'
         }
       ]
   }


  /**
   * 下拉操作栏
   */
  function getDropDownAction(record){
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft'
        },
        auth: 'books:inz_word_books:delete'
      }
    ]
  }

  /**
   * 打开保存提示词对话框
   */
  function handleSavePrompt() {
    promptModalVisible.value = true;
    promptText.value = '';
  }
  
  /**
   * 提交保存提示词
   */
  async function handlePromptSubmit() {
    // 校验输入
    if (!promptText.value || promptText.value.trim() === '') {
      createMessage.warning('请输入提示词内容');
      return;
    }
    
    try {
      promptModalLoading.value = true;
      
      // 构造参数并调用API
      const result = await savePrompt({
        prompt: promptText.value
      });
      
      if (result && result.success) {
        if (result.result && result.result.hash) {
          createMessage.success(`保存提示词成功，获取的Hash值为: ${result.result.hash}`);
        } else {
          createMessage.success('保存提示词成功');
        }
        promptModalVisible.value = false;
      } else {
        createMessage.error(result.message || '保存提示词失败');
      }
    } catch (error) {
      console.error('保存提示词失败:', error);
      createMessage.error('保存提示词失败');
    } finally {
      promptModalLoading.value = false;
    }
  }

</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>