import { BasicColumn, FormSchema } from '/@/components/Table';
import { defHttp } from '/@/utils/http/axios';

// 预加载字典数据
let wordsDict = [];
let usersDict = [];
let booksDict = [];
let chaptersDict = [];
// 移除educationDict，直接使用词书表的education_name字段

// 加载单词字典 - 使用分页接口获取大量数据
defHttp
  .get({
    url: '/words/inzWords/list',
    params: { pageNo: 1, pageSize: 10000 }, // 获取大量数据
  })
  .then((res) => {
    wordsDict = res?.records || [];
    console.log('单词数据加载成功，数量:', wordsDict.length, '示例:', wordsDict.slice(0, 3));
  })
  .catch((e) => console.error('单词数据加载失败:', e));

// 加载用户字典
defHttp
  .get({ url: '/user_front/inzUserFront/listAll' })
  .then((res) => {
    usersDict = res?.records || res || [];
    console.log('用户数据加载成功，数量:', usersDict.length, '示例:', usersDict.slice(0, 3));
  })
  .catch((e) => console.error('用户数据加载失败:', e));

// 加载图书字典
defHttp
  .get({
    url: '/books/inzWordBooks/list',
    params: { pageNo: 1, pageSize: 10000 },
  })
  .then((res) => {
    booksDict = res?.records || [];
    console.log('图书数据加载成功，数量:', booksDict.length);
    console.log('图书示例（含教育层次）:', booksDict.slice(0, 3).map(book => ({
      name: book.name,
      educationName: book.educationName
    })));
  })
  .catch((e) => console.error('图书数据加载失败:', e));

// 加载章节字典 - 使用listAll接口
defHttp
  .get({ url: '/books/inzWordBooks/chapterListAll' })
  .then((res) => {
    chaptersDict = res?.records || res || [];
    console.log('章节数据加载成功，数量:', chaptersDict.length);
  })
  .catch((e) => console.error('章节数据加载失败:', e));

// 不再需要单独加载教育分类字典，直接使用词书表的education_name字段

// 通过图书ID获取教育层次名称（直接从词书表获取）
const getEducationNameByBookId = (bookId: string) => {
  const book = booksDict.find((item) => item.id === bookId);
  return book?.educationName || '未知分类';
};

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '单词',
    align: 'center',
    dataIndex: 'wordId',
    customRender: ({ text }) => {
      const word = wordsDict.find((item) => item.id === text);
      if (word) {
        return word.word;
      }
      // 如果找不到单词，显示ID并添加调试信息
      console.warn('未找到单词ID:', text, '字典数量:', wordsDict.length);
      return text;
    },
  },
  {
    title: '用户姓名',
    align: 'center',
    dataIndex: 'createBy',
    customRender: ({ text }) => {
      const user = usersDict.find((item) => item.id === text);
      return user?.realName || text;
    },
  },
  {
    title: '手机号',
    align: 'center',
    dataIndex: 'createBy',
    customRender: ({ text }) => {
      const user = usersDict.find((item) => item.id === text);
      return user?.phone || '无手机号';
    },
  },
  {
    title: '学习状态',
    align: 'center',
    dataIndex: 'status',
    customRender: ({ text }) => {
      const statusMap = {
        0: '未归类',
        1: '生词',
        2: '学习中',
        3: '掌握',
      };
      return statusMap[text] || text;
    },
  },
  {
    title: '所属图书',
    align: 'center',
    dataIndex: 'bookId',
    customRender: ({ text }) => {
      const book = booksDict.find((item) => item.id === text);
      return book?.name || text;
    },
  },
  {
    title: '教育分类',
    align: 'center',
    dataIndex: 'bookId',
    customRender: ({ text }) => {
      return getEducationNameByBookId(text);
    },
  },
  {
    title: '所属章节',
    align: 'center',
    dataIndex: 'chapterId',
    customRender: ({ text }) => {
      const chapter = chaptersDict.find((item) => item.id === text);
      return chapter?.name || text;
    },
  },
  {
    title: '学习时间',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '最新学习时间',
    align: 'center',
    dataIndex: 'updateTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号',
    },
  },
  {
    label: '学习状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未归类', value: 0 },
        { label: '生词', value: 1 },
        { label: '学习中', value: 2 },
        { label: '掌握', value: 3 },
      ],
      placeholder: '请选择学习状态',
    },
  },
  {
    label: '所属图书',
    field: 'bookId',
    component: 'ApiSelect',
    componentProps: {
      api: () =>
        defHttp.get({
          url: '/books/inzWordBooks/list',
          params: { pageNo: 1, pageSize: 10000 },
        }),
      labelField: 'name',
      valueField: 'id',
      resultField: 'records',
      placeholder: '请选择图书',
      showSearch: true,
    },
  },
  {
    label: '用户',
    field: 'createBy',
    component: 'ApiSelect',
    componentProps: {
      api: () => defHttp.get({ url: '/user_front/inzUserFront/listAll' }),
      labelField: 'realName',
      valueField: 'id',
      resultField: 'records',
      placeholder: '请选择用户',
      showSearch: true,
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '单词',
    field: 'wordId',
    component: 'ApiSelect',
    componentProps: {
      api: () =>
        defHttp.get({
          url: '/words/inzWords/list',
          params: { pageNo: 1, pageSize: 10000 },
        }),
      labelField: 'word',
      valueField: 'id',
      resultField: 'records',
      placeholder: '请选择单词',
      showSearch: true,
      filterOption: (input: string, option: any) => {
        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      },
    },
  },
  {
    label: '学习状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未归类', value: 0 },
        { label: '生词', value: 1 },
        { label: '学习中', value: 2 },
        { label: '掌握', value: 3 },
      ],
    },
  },
  {
    label: '所属图书',
    field: 'bookId',
    component: 'ApiSelect',
    componentProps: {
      api: () =>
        defHttp.get({
          url: '/books/inzWordBooks/list',
          params: { pageNo: 1, pageSize: 10000 },
        }),
      labelField: 'name',
      valueField: 'id',
      resultField: 'records',
      placeholder: '请选择图书',
      showSearch: true,
    },
  },
  {
    label: '所属章节',
    field: 'chapterId',
    component: 'ApiSelect',
    componentProps: {
      api: () => defHttp.get({ url: '/books/inzWordBooks/chapterListAll' }),
      labelField: 'name',
      valueField: 'id',
      resultField: 'records',
      placeholder: '请选择章节',
      showSearch: true,
    },
  },
  {
    label: '教育分类',
    field: 'educationName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入教育分类名称',
    },
  },
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  wordId: { title: '单词id', order: 0, view: 'text', type: 'string' },
  status: { title: '0未归类  1生词 2学习中 3掌握', order: 1, view: 'number', type: 'number' },
  bookId: { title: '图书id', order: 2, view: 'text', type: 'string' },
  chapterId: { title: '章节id', order: 3, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
