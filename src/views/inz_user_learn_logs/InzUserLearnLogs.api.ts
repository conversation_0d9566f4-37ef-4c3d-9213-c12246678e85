import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/inz_user_learn_logs/inzUserLearnLogs/list',
  save='/inz_user_learn_logs/inzUserLearnLogs/add',
  edit='/inz_user_learn_logs/inzUserLearnLogs/edit',
  deleteOne = '/inz_user_learn_logs/inzUserLearnLogs/delete',
  deleteBatch = '/inz_user_learn_logs/inzUserLearnLogs/deleteBatch',
  importExcel = '/inz_user_learn_logs/inzUserLearnLogs/importExcel',
  exportXls = '/inz_user_learn_logs/inzUserLearnLogs/exportXls',
  // 获取关联数据的API
  wordsList = '/words/inzWords/list',
  usersList = '/user_front/inzUserFront/listAll',
  booksList = '/books/inzWordBooks/list',
  chaptersList = '/books/inzWordBooks/chapterListAll',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

// 获取单词列表
export const getWordsList = () => {
  return defHttp.get({
    url: Api.wordsList,
    params: { pageNo: 1, pageSize: 10000 }
  });
}

// 获取用户列表
export const getUsersList = () => {
  return defHttp.get({url: Api.usersList});
}

// 获取图书列表
export const getBooksList = () => {
  return defHttp.get({
    url: Api.booksList,
    params: { pageNo: 1, pageSize: 10000 }
  });
}

// 获取章节列表
export const getChaptersList = () => {
  return defHttp.get({url: Api.chaptersList});
}
