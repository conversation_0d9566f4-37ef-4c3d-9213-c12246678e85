<template>
  <BasicModal
    @register="registerModal"
    @ok="handleSubmit"
    :width="500"
    :title="`${modalData.isBatch ? '批量' : ''}调整金豆`"
    :titleStyle="{ color: '#1890ff', fontSize: '18px' }"
    :bodyStyle="{ padding: '24px' }"
  >
    <div class="beans-adjust-container">
      <!-- 批量操作提示 -->
      <a-alert
        v-if="modalData.isBatch"
        type="info"
        :message="`将影响 ${modalData.userIds.length} 位用户`"
        class="mb-4"
        show-icon
      />

      <!-- 调整数量表单 -->
      <a-form layout="vertical">
        <a-form-item
          label="调整数量"
          :validate-status="amountStatus"
          :help="amountHelpText"
        >
          <a-input-number
            v-model:value="formState.amount"
            :min="-999999"
            :max="999999"
            :step="100"
            :formatter="formatAmount"
            :parser="parseAmount"
            placeholder="输入正数增加/负数减少"
            style="width: 100%"
            size="large"
            @change="validateAmount"
          />
          <div class="amount-tips mt-2">
            <span>当前值: {{ formattedAmount }}</span>
            <a-tag :color="amountTagColor" class="ml-2">
              {{ amountTagText }}
            </a-tag>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { assignBeans } from "@/views/user_front/InzUserFront.api";
import { message } from "ant-design-vue";
import { defHttp } from '/@/utils/http/axios';

// 类型定义
interface ModalData {
  isBatch: boolean;
  userIds: string[];
  userData?: any[]; // 添加用户数据字段，用于存储用户的真实姓名
}

// 模态框数据（替代props）
const modalData = ref<ModalData>({
  isBatch: false,
  userIds: [],
  userData: []
});

// 表单状态
const formState = ref({
  amount: 100
});

// 金额格式化显示
const formatAmount = (value: number) => {
  return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 金额解析
const parseAmount = (value: string) => {
  return value.replace(/\$\s?|(,*)/g, '');
};

// 计算属性
const formattedAmount = computed(() => formatAmount(formState.value.amount));
const amountTagColor = computed(() =>
  formState.value.amount > 0 ? 'green' :
    formState.value.amount < 0 ? 'red' : 'default'
);
const amountTagText = computed(() =>
  formState.value.amount > 0 ? '增加金豆' :
    formState.value.amount < 0 ? '扣除金豆' : '无变化'
);

// 校验状态
const amountStatus = ref<'success' | 'error' | ''>('');
const amountHelpText = ref('');

// 金额校验
const validateAmount = () => {
  if (formState.value.amount === 0) {
    amountStatus.value = 'error';
    amountHelpText.value = '调整值不能为0';
  } else {
    amountStatus.value = '';
    amountHelpText.value = '';
  }
};

// 注册模态框
const [registerModal, { closeModal }] = useModalInner((data: ModalData) => {
  console.log('接收到模态框数据:', data);
  modalData.value = {
    isBatch: data.isBatch,
    userIds: data.userIds || [],
    userData: []
  };
  
  // 获取用户真实姓名数据
  if (data.userIds && data.userIds.length > 0) {
    fetchUserData(data.userIds);
  }
  
  // 重置表单状态
  formState.value.amount = 100;
  amountStatus.value = '';
});

// 获取用户真实姓名信息
const fetchUserData = async (userIds: string[]) => {
  try {
    // 批量获取用户信息
    const userData = await Promise.all(
      userIds.map(id => 
        defHttp.get({url: '/user_front/inzUserFront/queryById', params: {id}})
      )
    );
    modalData.value.userData = userData.filter(Boolean);
  } catch (e) {
    console.error('获取用户信息失败:', e);
  }
};

const emit = defineEmits(['success']);

// 提交处理
const handleSubmit = async () => {
  validateAmount();
  if (amountStatus.value === 'error') return;

  try {
    // 构建包含真实姓名的提交数据
    const submitData = {
      isBatch: modalData.value.isBatch,
      userIds: modalData.value.userIds,
      amount: formState.value.amount,
      includeRealNames: true, // 添加标记，告知后端需要包含真实姓名
      userData: modalData.value.userData // 将用户数据传递给后端
    };
    
    console.log('提交数据:', submitData);

    await assignBeans(submitData);

    message.success(
      modalData.value.isBatch
        ? `已批量调整${modalData.value.userIds.length}位用户的金豆`
        : '金豆调整成功'
    );
    emit('success');
    closeModal();
  } catch (e) {
    console.error('操作失败:', e);
    message.error(`操作失败: ${e.message}`);
  }
};
</script>

<style lang="less" scoped>
.beans-adjust-container {
  padding: 16px;

  :deep(.ant-input-number) {
    width: 100%;
    height: 48px;
    font-size: 16px;

    .ant-input-number-input {
      height: 100%;
      font-size: 16px;
    }

    &-handler-wrap {
      opacity: 1;
      height: 100%;
    }
  }

  .amount-tips {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;

    .ant-tag {
      margin-left: 8px;
    }
  }
}
</style>
