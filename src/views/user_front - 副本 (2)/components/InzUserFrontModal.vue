<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
      <BasicForm @register="registerForm" ref="formRef" name="InzUserFrontForm" />
      <!-- 子表单区域 -->
<!--      <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">-->
<!--        <a-tab-pane tab="用户登录设备" key="inzUserDevice" :forceRender="true">-->
<!--          <JVxeTable-->
<!--            keep-source-->
<!--            resizable-->
<!--            ref="inzUserDevice"-->
<!--            :loading="inzUserDeviceTable.loading"-->
<!--            :columns="inzUserDeviceTable.columns"-->
<!--            :dataSource="inzUserDeviceTable.dataSource"-->
<!--            :height="340"-->
<!--            :disabled="formDisabled"-->
<!--            :rowNumber="true"-->
<!--            :rowSelection="true"-->
<!--            :toolbar="true"-->
<!--            />-->
<!--        </a-tab-pane>-->
<!--        <a-tab-pane tab="用户金豆记录" key="inzUserPayLog" :forceRender="true">-->
<!--          <JVxeTable-->
<!--            keep-source-->
<!--            resizable-->
<!--            ref="inzUserPayLog"-->
<!--            :loading="inzUserPayLogTable.loading"-->
<!--            :columns="inzUserPayLogTable.columns"-->
<!--            :dataSource="inzUserPayLogTable.dataSource"-->
<!--            :height="340"-->
<!--            :disabled="formDisabled"-->
<!--            :rowNumber="true"-->
<!--            :rowSelection="true"-->
<!--            :toolbar="true"-->
<!--            />-->
<!--        </a-tab-pane>-->
<!--      </a-tabs>-->
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { JVxeTable } from '/@/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    import {formSchema,inzUserDeviceJVxeColumns,inzUserPayLogJVxeColumns} from '../InzUserFront.data';
    import {saveOrUpdate,queryInzUserDevice,queryInzUserPayLog} from '../InzUserFront.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const formDisabled = ref(false);
    const refKeys = ref(['inzUserDevice', 'inzUserPayLog', ]);
    const activeKey = ref('inzUserDevice');
    const inzUserDevice = ref();
    const inzUserPayLog = ref();
    const tableRefs = {inzUserDevice, inzUserPayLog, };
    const inzUserDeviceTable = reactive({
          loading: false,
          dataSource: [],
          columns:inzUserDeviceJVxeColumns
    })
    const inzUserPayLogTable = reactive({
          loading: false,
          dataSource: [],
          columns:inzUserPayLogJVxeColumns
    })
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        formDisabled.value = !data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
             requestSubTableData(queryInzUserDevice, {id:data?.record?.id}, inzUserDeviceTable)
             requestSubTableData(queryInzUserPayLog, {id:data?.record?.id}, inzUserPayLogTable)
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys);

    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

    async function reset(){
      await resetFields();
      activeKey.value = 'inzUserDevice';
      inzUserDeviceTable.dataSource = [];
      inzUserPayLogTable.dataSource = [];
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
           inzUserDeviceList: allValues.tablesValue[0].tableData,
           inzUserPayLogList: allValues.tablesValue[1].tableData,
         }
       }
    //表单提交事件
    async function requestAddOrEdit(values) {
        try {
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
