<!-- src/views/user_front/components/UpdatePasswordModal.vue -->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import { FormSchema } from '/@/components/Table';
import { updatePassword } from '../InzUserFront.api';
import { useMessage } from '/@/hooks/web/useMessage';

const { createMessage } = useMessage();
const userId = ref<string>('');
const title = computed(() => `修改密码`);

const formSchemas: FormSchema[] = [
  {
    field: 'newPassword',
    label: '新密码',
    component: 'InputPassword',
    required: true,
    rules: [
      { required: true, message: '请输入新密码' },
      { min: 6, message: '密码长度不能少于6位' }
    ]
  },
  {
    field: 'confirmPassword',
    label: '确认密码',
    component: 'InputPassword',
    required: true,
    rules: [
      { required: true, message: '请确认密码' },
      {
        validator: (_, value) => {
          if (value !== getFieldsValue().newPassword) {
            return Promise.reject('两次输入的密码不一致');
          }
          return Promise.resolve();
        }
      }
    ]
  }
];

const [registerForm, { validate, getFieldsValue, resetFields }] = useForm({
  labelWidth: 100,
  schemas: formSchemas,
  showActionButtonGroup: false
});

const [registerModal, { setModalProps, closeModal }] = useModalInner((data) => {
  userId.value = data.record.id;
  resetFields();
});

async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });

    await updatePassword({
      userId: userId.value,
      newPassword: values.newPassword
    });
    closeModal();
  } catch (error) {
    console.error('修改密码失败', error);
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
