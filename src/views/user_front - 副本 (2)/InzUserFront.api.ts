import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/user_front/inzUserFront/list',
  save='/user_front/inzUserFront/add',
  edit='/user_front/inzUserFront/edit',
  deleteOne = '/user_front/inzUserFront/delete',
  deleteBatch = '/user_front/inzUserFront/deleteBatch',
  importExcel = '/user_front/inzUserFront/importExcel',
  exportXls = '/user_front/inzUserFront/exportXls',
  inzUserDeviceList = '/user_front/inzUserFront/queryInzUserDeviceByMainId',
  inzUserPayLogList = '/user_front/inzUserFront/queryInzUserPayLogByMainId',
  listAll = '/user_front/inzUserFront/listAll',
  gradeList = '/grade/inzGrade/listAll',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzUserDevice = Api.inzUserDeviceList
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzUserPayLog = Api.inzUserPayLogList
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

export const getUserFrontList = (params) => {
  return defHttp.get({ url: Api.listAll, params });
};
export const getGradeList = (params) => {
  return defHttp.get({ url: Api.gradeList, params });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
/**
 * 子表列表接口
 * @param params
 */
export const inzUserDeviceList = (params) =>
  defHttp.get({url: Api.inzUserDeviceList, params},{isTransformResponse:false});
/**
 * 子表列表接口
 * @param params
 */
export const inzUserPayLogList = (params) =>
  defHttp.get({url: Api.inzUserPayLogList, params},{isTransformResponse:false});

export const assignBeans = (params) => {
  return defHttp.post({
    url: '/user_front/inzUserFront/assignBeans',
    params
  });
};
// 修改密码接口
export const updatePassword = (params: { userId: string; newPassword: string }) => {
  return defHttp.put({ url: '/user_front/inzUserFront/updatePassword', params });
};
