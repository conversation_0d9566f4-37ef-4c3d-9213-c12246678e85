<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--展开行插槽-->
      <template #expandedRowRender="{ record }">
        <SubCategoriesTable :parentId="record.id" :parentName="record.categoryName" @edit="handleEditSubCategory" />
      </template>
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'inz_learning_categorys:inz_learning_category:add'" @click="handleAdd" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
        <a-button
          type="primary"
          v-auth="'inz_learning_categorys:inz_learning_category:exportXls'"
          preIcon="ant-design:export-outlined"
          @click="onExportXls"
        >
          导出
        </a-button>
        <!-- 新增：子分类查看按钮 -->
        <a-button type="default" @click="showSubCategoriesModal" preIcon="ant-design:eye-outlined"> 查看子分类 </a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'inz_learning_categorys:inz_learning_category:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="getTableAction(record)"
          :dropDownActions="getDropDownAction(record)"
          stopButtonPropagation
        />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }"></template>
    </BasicTable>

    <!-- 新增：子分类查看模态框 -->
    <a-modal v-model:visible="subCategoriesVisible" title="子分类管理" width="1200px" :footer="null" destroyOnClose>
      <div v-if="parentCategories.length === 0" class="empty-state">
        <a-empty description="暂无父分类数据" />
      </div>
      <a-tabs v-else v-model:activeKey="activeTabKey" type="card">
        <a-tab-pane v-for="parentCategory in parentCategories" :key="parentCategory.id" :tab="parentCategory.categoryName">
          <SubCategoriesTable :parentId="parentCategory.id" :parentName="parentCategory.categoryName" @edit="handleEditSubCategory" />
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 表单区域 -->
    <InzLearningCategorysModal @register="registerModal" @success="handleSuccess"></InzLearningCategorysModal>
  </div>
</template>

<script lang="ts" name="inz_learning_categorys-inzLearningCategorys" setup>
  import { reactive, ref, h } from 'vue'; // 新增：导入h函数
  import { Modal } from 'ant-design-vue'; // 新增：导入Modal
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import InzLearningCategorysModal from './components/InzLearningCategorysModal.vue';
  import SubCategoriesTable from './components/SubCategoriesTable.vue';
  import { columns, searchFormSchema, superQuerySchema } from './InzLearningCategorys.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from './InzLearningCategorys.api';
  import { useUserStore } from '/@/store/modules/user';
  import { addSubCategory } from './InzLearningCategorys.api'; // 新增：导入添加子分类API

  const { message } = useMessage();
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();

  // 新增：子分类模态框相关状态
  const subCategoriesVisible = ref(false);
  const activeTabKey = ref('');
  const parentCategories = ref([]);

  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '继续深造-分类',
      api: list,
      columns,
      canResize: false,
      expandRowByClick: true, // 启用点击行展开
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        console.log('🔍 分类列表查询参数:', params);
        return Object.assign(params, queryParam);
      },
      afterFetch: (data) => {
        console.log('📦 分类列表原始数据:', data);
        // 暂时显示所有数据，便于调试
        return data;
      },
    },
    exportConfig: {
      name: '继续深造-分类',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 新增：显示子分类模态框
   */
  async function showSubCategoriesModal() {
    try {
      // 获取所有父分类（level = 1）
      const res = await list({ level: 1, pageNo: 1, pageSize: 1000 });
      if (res.success && res.result?.records) {
        parentCategories.value = res.result.records;
        if (parentCategories.value.length > 0) {
          activeTabKey.value = parentCategories.value[0].id;
          subCategoriesVisible.value = true;
        } else {
          message.warning('暂无父分类数据');
        }
      }
    } catch (error) {
      console.error('获取父分类失败:', error);
      message.error('获取父分类失败');
    }
  }

  /**
   * 新增：编辑子分类
   */
  function handleEditSubCategory(record: any) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 新增：添加下级分类
   */
  async function handleAddSubCategory(record: Recordable) {
    const subCategoryName = await new Promise((resolve) => {
      let inputValue = ''; // 用变量存储输入值
      
      const modal = Modal.confirm({
        title: `为"${record.categoryName}"添加子分类`,
        content: h('div', [
          h('p', '请输入子分类名称：'),
          h('input', {
            style: 'width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;',
            placeholder: '请输入子分类名称',
            value: inputValue, // 绑定初始值
            onInput: (e) => {
              inputValue = e.target.value; // 实时更新值
            },
            onKeyup: (e) => {
              if (e.key === 'Enter') {
                const value = inputValue.trim();
                if (value) {
                  modal.destroy();
                  resolve(value);
                } else {
                  message.warning('请输入子分类名称');
                }
              }
            }
          })
        ]),
        okText: '确认添加',
        cancelText: '取消',
        onOk: () => {
          const value = inputValue.trim(); // 直接使用变量中的值
          if (value) {
            resolve(value);
          } else {
            message.warning('请输入子分类名称');
            return Promise.reject();
          }
        },
        onCancel: () => {
          resolve(null);
        }
      });
      
      // 自动聚焦到输入框
      setTimeout(() => {
        const inputs = document.querySelectorAll('.ant-modal input');
        const lastInput = inputs[inputs.length - 1]; // 获取最后一个input（最新的modal）
        lastInput?.focus();
      }, 100);
    });
    
    if (subCategoryName) {
      try {
        await addSubCategory({
          categoryId: record.id,
          subCategoryName: subCategoryName
        });
        message.success('子分类添加成功');
        reload(); // 刷新列表
      } catch (error) {
        console.error('添加子分类失败:', error);
        message.error('添加子分类失败');
      }
    }
  }
  
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'inz_learning_categorys:inz_learning_category:edit',
      },
      {
        label: '添加下级',
        onClick: handleAddSubCategory.bind(null, record),
        auth: 'inz_learning_categorys:inz_learning_category:add', // 使用添加权限
        ifShow: () => record.level === 1, // 只在一级分类显示
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'inz_learning_categorys:inz_learning_category:delete',
      },
    ];
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }

  .empty-state {
    padding: 40px;
    text-align: center;
  }
</style>
