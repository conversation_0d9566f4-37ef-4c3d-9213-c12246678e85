<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--展开行插槽-->
      <template #expandedRowRender="{ record }">
        <SubCategoriesTable :parentId="record.id" :parentName="record.categoryName" @edit="handleEditSubCategory" />
      </template>
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'inz_learning_categorys:inz_learning_category:add'" @click="handleAdd" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
        <a-button
          type="primary"
          v-auth="'inz_learning_categorys:inz_learning_category:exportXls'"
          preIcon="ant-design:export-outlined"
          @click="onExportXls"
        >
          导出
        </a-button>
        <!-- 新增：子分类查看按钮 -->
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'inz_learning_categorys:inz_learning_category:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="getTableAction(record)"
          :dropDownActions="getDropDownAction(record)"
          stopButtonPropagation
        />
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }"></template>
    </BasicTable>

    <!-- 新增：子分类查看模态框 -->
    <a-modal v-model:visible="subCategoriesVisible" title="子分类管理" width="1200px" :footer="null" destroyOnClose>
      <div v-if="parentCategories.length === 0" class="empty-state">
        <a-empty description="暂无父分类数据" />
      </div>
      <a-tabs v-else v-model:activeKey="activeTabKey" type="card">
        <a-tab-pane v-for="parentCategory in parentCategories" :key="parentCategory.id" :tab="parentCategory.categoryName">
          <SubCategoriesTable :parentId="parentCategory.id" :parentName="parentCategory.categoryName" @edit="handleEditSubCategory" />
        </a-tab-pane>
      </a-tabs>
    </a-modal>

    <!-- 表单区域 -->
    <InzLearningCategorysModal @register="registerModal" @success="handleSuccess"></InzLearningCategorysModal>
  </div>
</template>

<script lang="ts" name="inz_learning_categorys-inzLearningCategorys" setup>
  import { reactive, ref, h } from 'vue'; // 新增：导入h函数
  import { Modal } from 'ant-design-vue'; // 新增：导入Modal
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import InzLearningCategorysModal from './components/InzLearningCategorysModal.vue';
  import SubCategoriesTable from './components/SubCategoriesTable.vue';
  import { columns, searchFormSchema, superQuerySchema } from './InzLearningCategorys.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from './InzLearningCategorys.api';
  import { useUserStore } from '/@/store/modules/user';
  import { addSubCategory } from './InzLearningCategorys.api'; // 新增：导入添加子分类API

  const { message } = useMessage();
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();

  // 新增：子分类模态框相关状态
  const subCategoriesVisible = ref(false);
  const activeTabKey = ref('');
  const parentCategories = ref([]);

  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '继续深造-分类',
      api: list,
      columns,
      canResize: false,
      expandRowByClick: true, // 启用点击行展开
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 160, // 增加宽度以容纳两个按钮
        fixed: 'right',
      },
      beforeFetch: (params) => {
        console.log('🔍 分类列表查询参数:', params);
        return Object.assign(params, queryParam);
      },
      afterFetch: (data) => {
        console.log('📦 分类列表原始数据:', data);
        // 暂时显示所有数据，便于调试
        return data;
      },
    },
    exportConfig: {
      name: '继续深造-分类',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 新增：显示子分类模态框
   */
  async function showSubCategoriesModal() {
    try {
      // 获取所有父分类（level = 1）
      const res = await list({ level: 1, pageNo: 1, pageSize: 1000 });
      if (res.success && res.result?.records) {
        parentCategories.value = res.result.records;
        if (parentCategories.value.length > 0) {
          activeTabKey.value = parentCategories.value[0].id;
          subCategoriesVisible.value = true;
        } else {
          message.warning('暂无父分类数据');
        }
      }
    } catch (error) {
      console.error('获取父分类失败:', error);
      message.error('获取父分类失败');
    }
  }

  /**
   * 新增：编辑子分类
   */
  function handleEditSubCategory(record: any) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 新增：添加下级分类
   */
  async function handleAddSubCategory(record: Recordable) {
    try {
      const subCategoryName = await new Promise<string | null>((resolve, reject) => {
        let inputElement: HTMLInputElement | null = null;
        let modalInstance: any = null;
        let inputValue = ''; // 使用局部变量存储输入值
  
        const handleConfirm = () => {
          // 优先使用局部变量，备用方案使用DOM元素
          const value = inputValue || inputElement?.value?.trim() || '';
          console.log('🔍 确认按钮输入值:', value);
          if (value) {
            modalInstance?.destroy();
            resolve(value);
          } else {
            message.warning('请输入子分类名称');
            // 不关闭模态框，让用户继续输入
          }
        };
  
        const handleCancel = () => {
          modalInstance?.destroy();
          resolve(null);
        };
  
        modalInstance = Modal.confirm({
          title: `为"${record.categoryName}"添加子分类`,
          content: h('div', [
            h('p', { style: 'margin-bottom: 12px; color: #666;' }, '请输入子分类名称：'),
            h('input', {
              ref: (el) => {
                inputElement = el as HTMLInputElement;
              },
              style: 'width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px;',
              placeholder: '请输入子分类名称',
              onInput: (e) => {
                // 实时更新输入值
                inputValue = (e.target as HTMLInputElement).value;
              },
              onKeyup: (e) => {
                if (e.key === 'Enter') {
                  const value = inputValue || (e.target as HTMLInputElement).value.trim();
                  console.log('🔍 Enter键输入值:', value);
                  if (value) {
                    modalInstance?.destroy();
                    resolve(value);
                  } else {
                    message.warning('请输入子分类名称');
                  }
                }
              }
            })
          ]),
          okText: '确认添加',
          cancelText: '取消',
          onOk: handleConfirm,
          onCancel: handleCancel
        });
  
        // 自动聚焦到输入框
        setTimeout(() => {
          if (inputElement) {
            inputElement.focus();
          } else {
            // 备用方案：通过选择器查找
            const inputs = document.querySelectorAll('.ant-modal input');
            const lastInput = inputs[inputs.length - 1] as HTMLInputElement;
            lastInput?.focus();
          }
        }, 100);
      });
  
      console.log('🔍 最终获取的子分类名称:', subCategoryName);
      console.log('🔍 父分类信息:', { id: record.id, name: record.categoryName });
  
      if (subCategoryName) {
        const requestData = {
          categoryId: record.id,
          subCategoryName: subCategoryName
        };
        console.log('📤 发送给后端的数据:', requestData);
  
        await addSubCategory(requestData);
        message.success('子分类添加成功');
        reload(); // 刷新列表
      } else {
        console.log('❌ 用户取消添加子分类');
      }
    } catch (error) {
      console.error('添加子分类过程中发生错误:', error);
      message.error('添加子分类失败');
    }
  }
  
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'inz_learning_categorys:inz_learning_category:edit',
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    const actions = [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
    
    // 只在一级分类显示添加下级选项
    if (record.level === 1) {
      actions.unshift({
        label: '添加下级',
        onClick: handleAddSubCategory.bind(null, record),
        auth: 'inz_learning_categorys:inz_learning_category:add',
      });
    }
    
    actions.push({
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        confirm: handleDelete.bind(null, record),
      },
      auth: 'inz_learning_categorys:inz_learning_category:delete',
    });
    
    return actions;
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }

  .empty-state {
    padding: 40px;
    text-align: center;
  }
</style>
