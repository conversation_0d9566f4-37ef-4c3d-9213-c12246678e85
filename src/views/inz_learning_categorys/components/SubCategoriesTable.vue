<template>
  <div>
    <div v-if="parentName" class="parent-info">
      <a-tag color="blue">{{ parentName }}</a-tag>
      <span class="sub-count">共 {{ dataSource.length }} 个子分类</span>
    </div>
    
    <BasicTable
      bordered
      size="small"
      :loading="loading"
      rowKey="id"
      :canResize="false"
      :columns="enhancedSubCategoriesColumns"
      :dataSource="dataSource"
      :pagination="false"
      :scroll="{ x: 1000 }"
      class="sub-categories-table"
    >
      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
          <a-popconfirm title="确定删除吗？" @confirm="handleDelete(record)">
            <a-button type="link" size="small" danger>删除</a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watchEffect, computed } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { subCategoriesColumns } from '../InzLearningCategorys.data';
  import { getSubCategories, deleteOne } from '../InzLearningCategorys.api';

  const { message } = useMessage();

  interface Props {
    parentId: string;
    parentName?: string; // 新增：父分类名称
  }

  const props = defineProps<Props>();
  const loading = ref(false);
  const dataSource = ref([]);

  // 增强的列配置，添加操作列
  const enhancedSubCategoriesColumns = computed(() => {
    return [
      ...subCategoriesColumns,
      {
        title: '操作',
        key: 'action',
        align: 'center',
        width: 120,
        slots: { customRender: 'action' },
      },
    ];
  });

  // 监听父ID变化，自动加载数据
  watchEffect(() => {
    if (props.parentId) {
      loadData(props.parentId);
    }
  });

  async function loadData(parentId: string) {
    if (!parentId) return;

    dataSource.value = [];
    loading.value = true;

    try {
      console.log('🔍 开始加载子分类数据，父分类ID:', parentId);
      console.log('📋 查询参数:', { parentId });

      const res = await getSubCategories(parentId);
      console.log('📦 子分类API返回结果:', res);
      console.log('✅ API调用成功状态:', res?.success);
      console.log('📄 返回数据结构:', res?.result);

      if (res.success) {
        // 处理返回的数据
        const result = res.result;
        if (Array.isArray(result)) {
          // 直接返回数组格式
          dataSource.value = result;
          console.log('子分类数据加载成功，共', result.length, '条记录');
        } else if (result && result.records) {
          // 分页格式
          dataSource.value = result.records;
          console.log('子分类数据加载成功，共', result.records.length, '条记录');
        } else {
          console.warn('子分类数据格式异常:', result);
          dataSource.value = [];
        }
      } else {
        console.warn('子分类API调用失败:', res);
        message.warning(res.message || '获取子分类数据失败');
        dataSource.value = [];
      }
    } catch (error) {
      console.error('获取子分类数据失败:', error);

      // 根据错误类型显示不同的提示
      if (error.message?.includes('Network')) {
        message.error('网络连接失败，请检查网络后重试');
      } else if (error.status === 403) {
        message.error('权限不足，无法查看子分类');
      } else if (error.status === 404) {
        message.warning('未找到相关子分类数据');
      } else {
        message.error('加载子分类失败，请稍后重试');
      }

      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * 新增：编辑子分类
   */
  function handleEdit(record: any) {
    // 触发编辑事件，可以通过emit传递给父组件
    emit('edit', record);
  }

  /**
   * 新增：删除子分类
   */
  async function handleDelete(record: any) {
    try {
      // 调用删除API
      await deleteOne({ id: record.id }, () => {
        message.success('删除成功');
        // 重新加载数据
        loadData(props.parentId);
      });
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  const emit = defineEmits(['edit', 'delete']);
</script>

<style scoped>
.parent-info {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.sub-count {
  color: #666;
  font-size: 14px;
}

.sub-categories-table {
  margin-top: 8px;
}
</style>

<style lang="less" scoped>
  .sub-categories-table {
    margin: 16px 0; // 上下间距

    :deep(.ant-table) {
      background-color: #fafafa; // 浅灰色背景区分层级
    }

    :deep(.ant-table-thead > tr > th) {
      background-color: #f0f0f0; // 表头背景色
      font-size: 13px; // 与主表格保持一致
      text-align: center; // 居中对齐
    }

    :deep(.ant-table-tbody > tr > td) {
      padding: 12px 8px; // 与主表格保持一致的内边距
      font-size: 13px; // 与主表格保持一致
      text-align: center; // 居中对齐
    }

    :deep(.ant-table-tbody > tr:hover > td) {
      background-color: #e6f7ff; // 悬停效果
    }

    // 确保表格边框和主表格一致
    :deep(.ant-table-bordered .ant-table-thead > tr > th),
    :deep(.ant-table-bordered .ant-table-tbody > tr > td) {
      border-color: #f0f0f0;
    }
  }
</style>
