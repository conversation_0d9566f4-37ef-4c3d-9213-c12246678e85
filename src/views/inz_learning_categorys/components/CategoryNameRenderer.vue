<template>
  <span v-if="loading" class="category-loading">
    <Spin size="small" />
  </span>
  <span v-else-if="categoryName" class="category-name">
    {{ categoryName }}
  </span>
  <span v-else class="category-error">
    未知分类
  </span>
</template>

<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { list } from '../InzLearningCategorys.api';

  // 组件属性定义
  interface Props {
    categoryId: string;
  }

  const props = defineProps<Props>();

  // 响应式数据
  const loading = ref(false);
  const categoryName = ref('');

  // 全局分类缓存 - 避免重复请求
  const categoryCache = new Map<string, string>();

  // 监听分类ID变化，自动加载分类信息
  watchEffect(async () => {
    console.log('🔄 CategoryNameRenderer watchEffect 触发，categoryId:', props.categoryId);

    if (!props.categoryId) {
      console.log('❌ categoryId 为空，跳过加载');
      categoryName.value = '';
      return;
    }

    // 检查缓存中是否已有该分类信息
    if (categoryCache.has(props.categoryId)) {
      const cachedName = categoryCache.get(props.categoryId)!;
      categoryName.value = cachedName;
      console.log('💾 从缓存获取分类名称:', cachedName);
      return;
    }
    
    // 开始加载分类信息
    loading.value = true;
    categoryName.value = '';
    
    try {
      console.log('🔍 开始加载分类信息，分类ID:', props.categoryId);
      console.log('📡 调用API接口:', '/inz_learning_categorys/inzLearningCategorys/list');
      console.log('📋 请求参数:', { id: props.categoryId });

      // 通过列表接口查询特定分类
      const res = await list({ id: props.categoryId });
      console.log('📦 分类API完整返回结果:', res);
      console.log('✅ API调用成功状态:', res?.success);
      console.log('📄 返回数据:', res?.result);

      if (res && res.success && res.result) {
        let categoryData = null;
        
        // 处理不同的返回格式
        if (Array.isArray(res.result)) {
          // 直接返回数组格式
          categoryData = res.result.find(item => item.id === props.categoryId);
        } else if (res.result.records && Array.isArray(res.result.records)) {
          // 分页格式
          categoryData = res.result.records.find(item => item.id === props.categoryId);
        } else if (res.result.id === props.categoryId) {
          // 单个对象格式
          categoryData = res.result;
        }

        if (categoryData) {
          const name = categoryData.categoryName || '未知分类';
          categoryName.value = name;

          // 将结果缓存起来
          categoryCache.set(props.categoryId, name);
          console.log('🎉 分类信息加载成功:', name);
        } else {
          console.warn('⚠️ 未找到对应的分类数据');
          categoryName.value = '';
          // 缓存空结果，避免重复请求
          categoryCache.set(props.categoryId, '');
        }
      } else {
        console.warn('⚠️ 分类API调用失败或数据为空:', res);
        console.warn('❌ 失败原因 - success:', res?.success, 'result:', res?.result);
        categoryName.value = '';
        // 缓存空结果，避免重复请求
        categoryCache.set(props.categoryId, '');
      }
    } catch (error) {
      console.error('💥 分类信息加载失败:', error);
      categoryName.value = '';
      // 缓存空结果，避免重复请求
      categoryCache.set(props.categoryId, '');
    } finally {
      loading.value = false;
    }
  });

  // 清除缓存的方法（可选，用于调试）
  const clearCache = () => {
    categoryCache.clear();
    console.log('🧹 分类缓存已清空');
  };

  // 获取缓存大小的方法（可选，用于调试）
  const getCacheSize = () => {
    return categoryCache.size;
  };

  // 导出方法供外部使用（可选）
  defineExpose({
    clearCache,
    getCacheSize,
  });
</script>

<style scoped>
  .category-loading {
    color: #999;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .category-name {
    color: #333;
    font-weight: normal;
  }

  .category-error {
    color: #999;
    font-style: italic;
    font-size: 12px;
  }

  /* 确保加载指示器与文本对齐 */
  .category-loading :deep(.ant-spin) {
    line-height: 1;
  }

  .category-loading :deep(.ant-spin-dot) {
    font-size: 12px;
  }
</style>
