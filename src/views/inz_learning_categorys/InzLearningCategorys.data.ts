import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { h } from 'vue';
import ModuleNameRenderer from './components/ModuleNameRenderer.vue';
import { defHttp } from '/@/utils/http/axios';

// 添加模块数据字典
let moduleDict = [];

// 预加载模块数据
defHttp
  .get({ url: '/inz_learning_modules/inzLearningModules/list', params: { pageNo: 1, pageSize: 10000 } })
  .then((res) => {
    moduleDict = res?.records || [];
  })
  .catch((e) => console.error('模块数据加载失败:', e));

const statusMap = {
  0: '禁用',
  1: '启用',
};

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '所属模块',
    align: 'center',
    dataIndex: 'moduleId',
    customRender: ({ text }) => {
      const module = moduleDict.find((item) => item.id === text);
      return module?.moduleName || text;
    }
  },
  {
    title: '分类名称',
    align: 'center',
    dataIndex: 'categoryName',
  },
  {
    title: '分类描述',
    align: 'center',
    dataIndex: 'description',
  },
  {
    title: '封面图片',
    align: 'center',
    dataIndex: 'coverImage',
    customRender: render.renderImage,
  },
  {
    title: '层级',
    align: 'center',
    dataIndex: 'level',
    customRender: ({ text }) => {
      return text === 1 ? '一级分类' : '二级分类';
    },
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortOrder',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return statusMap[text];
    },
  },
  {
    title: '总视频数量',
    align: 'center',
    dataIndex: 'totalVideos',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属模块',
    field: 'moduleId',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入所属模块!' }];
    },
  },
  {
    label: '分类名称',
    field: 'categoryName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入分类名称!' }];
    },
  },
  {
    label: '分类编码',
    field: 'categoryCode',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入分类编码!' }];
    },
  },
  {
    label: '分类描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '封面图片',
    field: 'coverImage',
    component: 'JImageUpload',
    componentProps: {
      fileMax: 0,
    },
  },
  {
    label: '父分类ID',
    field: 'parentId',
    component: 'Input',
  },
  {
    label: '层级',
    field: 'level',
    component: 'InputNumber',
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
  },
  {
    label: '状态',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '总视频数量',
    field: 'totalVideos',
    component: 'InputNumber',
  },
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  moduleId: { title: '所属模块', order: 0, view: 'text', type: 'string' },
  categoryName: { title: '分类名称', order: 1, view: 'text', type: 'string' },
  categoryCode: { title: '分类编码', order: 2, view: 'text', type: 'string' },
  description: { title: '分类描述', order: 3, view: 'textarea', type: 'string' },
  coverImage: { title: '封面图片', order: 4, view: 'text', type: 'string' },
  parentId: { title: '父分类ID', order: 5, view: 'text', type: 'string' },
  level: { title: '层级', order: 6, view: 'number', type: 'number' },
  sortOrder: { title: '排序号', order: 7, view: 'number', type: 'number' },
  status: { title: '状态', order: 8, view: 'number', type: 'number' },
  totalVideos: { title: '总视频数量', order: 9, view: 'number', type: 'number' },
};

// 子分类表格列配置
export const subCategoriesColumns: BasicColumn[] = [
  // 移除这个显示moduleId的列
  // {
  //   title: '所属模块',
  //   align: 'center',
  //   dataIndex: 'moduleId',
  //   width: 120,
  // },
  {
    title: '所属模块', // 修改标题为"所属模块"
    align: 'center',
    dataIndex: 'moduleId',
    width: 150,
    customRender: ({ text }) => {
      const module = moduleDict.find((item) => item.id === text);
      return module?.moduleName || text;
    },
  },
  {
    title: '分类名称',
    align: 'center',
    dataIndex: 'categoryName',
    width: 150,
  },
  {
    title: '分类描述',
    align: 'center',
    dataIndex: 'description',
    width: 150,
    ellipsis: true,
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortOrder',
    width: 80,
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return text === 1 ? '启用' : '禁用';
    },
  },
];

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
