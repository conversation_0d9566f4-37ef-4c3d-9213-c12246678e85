import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/inz_learning_categorys/inzLearningCategorys/list',
  save='/inz_learning_categorys/inzLearningCategorys/add',
  edit='/inz_learning_categorys/inzLearningCategorys/edit',
  deleteOne = '/inz_learning_categorys/inzLearningCategorys/delete',
  deleteBatch = '/inz_learning_categorys/inzLearningCategorys/deleteBatch',
  importExcel = '/inz_learning_categorys/inzLearningCategorys/importExcel',
  exportXls = '/inz_learning_categorys/inzLearningCategorys/exportXls',
  subCategories = '/inz_learning_categorys/inzLearningCategorys/sub-categories',
  addSubCategory = '/inz_learning_categorys/inzLearningCategorys/add-sub-category', // 新增：添加子分类接口
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 获取子分类列表
 * @param parentId 父分类ID
 */
export const getSubCategories = (parentId: string) => {
  console.log('🔍 调用子分类API，父分类ID:', parentId);
  console.log('📡 API路径:', Api.subCategories); // 修改：使用正确的子分类接口
  console.log('📋 请求参数:', { parentId, pageNo: 1, pageSize: 1000 });

  return defHttp.get({
    url: Api.subCategories, // 修改：使用专门的子分类接口
    params: {
      parentId, // 通过parentId筛选子分类
      pageNo: 1,
      pageSize: 1000
    },
  }).then(response => {
    console.log('📦 子分类API完整返回:', response);
    return response;
  }).catch(error => {
    console.error('💥 子分类API调用失败:', error);
    throw error;
  });
};

/**
 * 获取分类列表（用于下拉选择）
 * @param params 查询参数
 */
export const getCategoryList = (params = {}) => {
  return defHttp.get({
    url: Api.list,
    params: {
      pageNo: 1,
      pageSize: 1000, // 获取所有分类用于下拉选择
      ...params,
    },
  });
};

/**
 * 根据模块ID获取一级分类列表
 * @param moduleId 模块ID
 */
export const getFirstLevelCategoriesByModule = (moduleId: string) => {
  return getCategoryList({ 
    moduleId, 
    level: 1 // 只获取一级分类
  });
};

/**
 * 根据父分类ID获取二级分类列表（使用专门的子分类接口）
 * @param parentId 父分类ID
 */
export const getSecondLevelCategories = (parentId: string) => {
  return getSubCategories(parentId); // 使用现有的 getSubCategories 函数
};

/**
 * 添加子分类
 * @param params 包含categoryId和subCategoryName的参数
 */
export const addSubCategory = (params: { categoryId: string; subCategoryName: string }) => {
  console.log('🔍 调用添加子分类API，参数:', params);
  return defHttp.post({
    url: Api.addSubCategory,
    params,
  }).then(response => {
    console.log('📦 添加子分类API返回:', response);
    return response;
  }).catch(error => {
    console.error('💥 添加子分类API调用失败:', error);
    throw error;
  });
};
