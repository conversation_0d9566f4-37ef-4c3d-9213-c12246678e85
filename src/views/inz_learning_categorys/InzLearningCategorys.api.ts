import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/inz_learning_categorys/inzLearningCategorys/list',
  save='/inz_learning_categorys/inzLearningCategorys/add',
  edit='/inz_learning_categorys/inzLearningCategorys/edit',
  deleteOne = '/inz_learning_categorys/inzLearningCategorys/delete',
  deleteBatch = '/inz_learning_categorys/inzLearningCategorys/deleteBatch',
  importExcel = '/inz_learning_categorys/inzLearningCategorys/importExcel',
  exportXls = '/inz_learning_categorys/inzLearningCategorys/exportXls',
  subCategories = '/inz_learning_categorys/inzLearningCategorys/sub-categories', // 子分类查询接口
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 获取子分类列表
 * @param parentId 父分类ID
 */
export const getSubCategories = (parentId: string) => {
  return defHttp.get({
    url: Api.subCategories,
    params: { parentId },
  });
};
