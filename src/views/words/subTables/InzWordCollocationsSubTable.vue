<template>
  <div>
      <!--引用表格-->
     <BasicTable bordered size="middle" :loading="loading" rowKey="id" :canResize="false" :columns="inzWordCollocationsColumns" :dataSource="dataSource" :pagination="false">
        <!--字段回显插槽-->
        <template v-slot:bodyCell="{ column, record, index, text }">
          <template v-if="column.dataIndex==='audioUrl'">
            <!-- 文件字段回显插槽 -->
            <span v-if="!text" style="font-size: 12px;font-style: italic;">无音频</span>
            <a-button v-else :ghost="true" type="primary" preIcon="ant-design:play-circle-outlined" size="small" @click="playAudio(text)">播放</a-button>
          </template>
        </template>
      </BasicTable>
    </div>
</template>

<script lang="ts" setup>
  import {ref,watchEffect} from 'vue';
  import {BasicTable} from '/@/components/Table';
  import {inzWordCollocationsColumns} from '../InzWords.data';
  import {inzWordCollocationsList} from '../InzWords.api';
  import { downloadFile } from '/@/utils/common/renderUtils';

  const props = defineProps({
    id: {
       type: String,
       default: '',
     },
  })

  const loading = ref(false);
  const dataSource = ref([]);

  watchEffect(() => {
      props.id && loadData(props.id);
   });

   function loadData(id) {
         dataSource.value = []
         loading.value = true
          inzWordCollocationsList({id}).then((res) => {
           if (res.success) {
              dataSource.value = res.result.records
           }
         }).finally(() => {
           loading.value = false
         })
    }
  function playAudio(url: string) {
    const audio = new Audio(url);
    audio.play().catch(error => {
      console.error("音频播放失败", error);
    });
  }
</script>
