import { BasicColumn, FormSchema } from '/@/components/Table';
import { JVxeColumn, JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
import { defHttp } from '@/utils/http/axios';

let bookDict = [];
let chapterDict = [];

// 加载图书字典
defHttp
  .get({ url: '/books/inzWordBooks/listAll' })
  .then((res) => {
    bookDict = res || [];
  })
  .catch((e) => console.error('图书数据加载失败:', e));
defHttp
  .get({ url: '/books/inzWordBooks/chapterListAll' })
  .then((res) => {
    chapterDict = res || [];
  })
  .catch((e) => console.error('图书数据加载失败:', e));
//列表数据
export const columns: BasicColumn[] = [
  {
    title: 'ID',
    align: 'center',
    dataIndex: 'id',
  },
  {
    title: '所属图书',
    align: 'center',
    dataIndex: 'bookId',
    customRender: ({ text }) => {
      const book = bookDict.find((item) => item.id === text);
      return book?.name || text;
    },
  },
  {
    title: '所属章节',
    align: 'center',
    dataIndex: 'chapterId',
    width: 200,
    customRender: ({ text }) => {
      const chatper = chapterDict.find((item) => item.id === text);
      return chatper?.name || text;
    },
  },
  {
    title: '单词',
    align: 'center',
    dataIndex: 'word',
  },
  {
    title: '英式音标',
    align: 'center',
    dataIndex: 'ukIpa',
  },
  {
    title: '美式音标',
    align: 'center',
    dataIndex: 'usIpa',
  },
  // {
  //   title: "状态",
  //   align: "center",
  //   dataIndex: "status",
  //   customRender: ({ text }) => {
  //     return render.renderSwitch(text, [{ text: "正常", value: "1" }, { text: "隐藏", value: "2" }]);
  //   }
  // },
  {
    title: '单独音标',
    align: 'center',
    dataIndex: 'pronunciationGuide',
  },
  {
    title: '拆分含义',
    align: 'center',
    dataIndex: 'rootParticlesMean',
  },
  {
    title: '谐音',
    align: 'center',
    dataIndex: 'homophonic',
  },
  {
    title: '英式发音音频',
    align: 'center',
    dataIndex: 'audioUrl',
  },
  {
    title: '美式发音音频',
    align: 'center',
    dataIndex: 'usAudioUrl',
  },
  {
    title: '自然拼读音频',
    align: 'center',
    dataIndex: 'naturalAudioUrl',
  },
  {
    title: '拆分单词音频',
    align: 'center',
    dataIndex: 'breakdownAudioUrl',
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '修改时间',
    align: 'center',
    dataIndex: 'updateTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '所属词书',
    field: 'bookId',
    component: 'ApiSelect',
    componentProps: ({ formModel, formActionType }) => {
      return {
        mode: 'multiple',
        api: () => defHttp.get({ url: '/books/inzWordBooks/listAll' }),
        //数值转成String
        numberToString: false,
        //标题字段
        labelField: 'name',
        //值字段
        valueField: 'id',
        //请求参数
        params: {},
        //返回结果字段
        resultField: 'records',
        onChange: async (value: any) => {
          // 当词书选择变化时，更新课节选项
          if (value) {
            const chapters = await defHttp.get({
              url: '/books/inzWordBooks/queryInzWordBookChapterByMainIds',
              params: { id: value }, // 将选中的词书ID传递给后端
            });
            const { updateSchema } = formActionType;
            formModel.chapterId = undefined;
            updateSchema({
              field: 'chapterId',
              componentProps: {
                options: chapters.records.map((item) => ({
                  label: item.name, // 显示字段
                  value: item.id, // 值字段
                })),
              },
            });
          }
        },
      };
    },
  },
  {
    label: '生成状态',
    field: 'generateStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未生成', value: '1' },
        { label: '生成完成', value: '2' },
      ],
      placeholder: '请选择生成状态',
    },
  },
  {
    label: '排序方式',
    field: 'sortBy',
    component: 'Select',
    componentProps: {
      options: [
        { label: '单词首字母', value: '1' },
        { label: '创建时间', value: '' },
      ],
      placeholder: '请选择生成状态',
    },
  },
  {
    label: '单词名称',
    field: 'word',
    component: 'Input',
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属词书',
    field: 'bookId',
    component: 'ApiSelect',
    componentProps: ({ formModel, formActionType }) => {
      return {
        api: () => defHttp.get({ url: '/books/inzWordBooks/listAll' }),
        //数值转成String
        numberToString: false,
        //标题字段
        labelField: 'name',
        //值字段
        valueField: 'id',
        //请求参数
        params: {},
        //返回结果字段
        resultField: 'records',
        onChange: async (value: any) => {
          // 当词书选择变化时，更新课节选项
          if (value) {
            const chapters = await defHttp.get({
              url: '/books/inzWordBooks/queryInzWordBookChapterByMainId',
              params: { id: typeof value === 'string' ? value : value[0] }, // 将选中的词书ID传递给后端
            });
            const { updateSchema, setFieldsValue } = formActionType;
            updateSchema({
              field: 'chapterId',
              componentProps: {
                options: chapters.records.map((item) => ({
                  label: item.name, // 显示字段
                  value: item.id, // 值字段
                })),
              },
            });
            setFieldsValue({ chapterId: formModel.chapterId });
          }
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入单词!' }];
    },
  },
  {
    label: '所属课节',
    field: 'chapterId',
    component: 'Select',
    componentProps: {
      options: '', // defalut []
      placeholder: '请选择课节',
      //数值转成String
      numberToString: false,
      //标题字段
      labelField: 'name',
      //值字段
      valueField: 'id',
    },
  },
  {
    label: '单词',
    field: 'word',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入单词!' }];
    },
  },
  {
    label: '英式音标',
    field: 'ukIpa',
    component: 'Input',
  },
  {
    label: '美式音标',
    field: 'usIpa',
    component: 'Input',
  },
  // {
  //   label: "状态",
  //   field: "status",
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '启用', value: 1 },
  //       { label: '停用', value: 0 },
  //     ],
  //   },
  // },
  {
    label: '单独音标',
    field: 'pronunciationGuide',
    component: 'Input',
  },
  {
    label: '拆分含义',
    field: 'rootParticlesMean',
    component: 'Input',
  },
  {
    label: '谐音',
    field: 'homophonic',
    component: 'Input',
  },
  {
    label: '谐音',
    field: 'homophonic',
    component: 'Input',
  },
  {
    label: '单词音频文件',
    field: 'audioUrl',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '美式发音音频文件',
    field: 'usAudioUrl',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '自然拼读音频文件',
    field: 'naturalAudioUrl',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '拆分单词音频文件',
    field: 'breakdownAudioUrl',
    component: 'JUpload',
    componentProps: {},
  },

  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表列表数据
export const inzWordCollocationsColumns: BasicColumn[] = [
  // {
  //  title: '单词id',
  //  align:"center",
  //  dataIndex: 'wordId'
  // },
  {
    title: '英文固定搭配',
    align: 'center',
    dataIndex: 'english',
  },
  {
    title: '中文含义',
    align: 'center',
    dataIndex: 'chinese',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type',
    customRender: ({ text }) => {
      let re = '';
      if (text === 'examine') {
        re = '例句';
      } else if (text === 'collection') {
        re = '固定搭配';
      } else if (text === 'natural_phonics') {
        re = '自然拼读';
      } else if (text === 'part_of_speech') {
        re = '词性';
      } else if (text === 'root_particles') {
        re = '拆分单词';
      } else if (text === 'speak_naturl_phonics') {
        re = '单词的拼读顺序';
      } else if (text === 'transformation') {
        re = '变形';
      }
      return re;
    },
  },
  {
    title: '音频文件',
    align: 'center',
    dataIndex: 'audioUrl',
  },
  {
    title: '排序',
    align: 'center',
    dataIndex: 'sort',
  },
];
//子表列表数据
export const inzWordsEtymologyColumns: BasicColumn[] = [
  // {
  //  title: '单词id',
  //  align:"center",
  //  dataIndex: 'wordId'
  // },
  {
    title: '词源中文解释',
    align: 'center',
    dataIndex: 'originCh',
  },
  {
    title: '意义演变',
    align: 'center',
    dataIndex: 'meaningEvolution',
  },
];
//子表表格配置
export const inzWordCollocationsJVxeColumns: JVxeColumn[] = [
  // {
  //   title: '单词id',
  //   key: 'wordId',
  //   type: JVxeTypes.input,
  //   width:"200px",
  //   placeholder: '请输入${title}',
  //   defaultValue:'',
  // },
  {
    title: '英文固定搭配',
    key: 'english',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '中文含义',
    key: 'chinese',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '类型',
    key: 'type',
    type: JVxeTypes.select,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    options: [
      // 下拉选项
      { title: '例句', value: 'examine' },
      { title: '固定搭配', value: 'collection' },
      { title: '自然拼读', value: 'natural_phonics' },
      { title: '词性', value: 'part_of_speech' },
      { title: '拆分单词', value: 'root_particles' },
      { title: '单词的拼读顺序', value: 'speak_naturl_phonics' },
      { title: '变形', value: 'transformation' },
    ],
  },
  {
    title: '音频文件',
    key: 'audioUrl',
    type: JVxeTypes.file,
  },
  {
    title: '排序',
    key: 'sort',
    type: JVxeTypes.inputNumber,
  },
];
export const inzWordsEtymologyJVxeColumns: JVxeColumn[] = [
  // {
  //   title: '单词id',
  //   key: 'wordId',
  //   type: JVxeTypes.input,
  //   width:"200px",
  //   placeholder: '请输入${title}',
  //   defaultValue:'',
  // },
  {
    title: '词源中文解释',
    key: 'originCh',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '意义演变',
    key: 'meaningEvolution',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  word: { title: '单词', order: 0, view: 'text', type: 'string' },
  ukIpa: { title: '英式音标', order: 1, view: 'text', type: 'string' },
  usIpa: { title: '美式音标', order: 2, view: 'text', type: 'string' },
  // status: { title: "状态", order: 3, view: "number", type: "number" },
  pronunciationGuide: { title: '单独音标', order: 4, view: 'text', type: 'string' },
  rootParticlesMean: { title: '拆分含义', order: 5, view: 'text', type: 'string' },
  homophonic: { title: '谐音', order: 6, view: 'text', type: 'string' },
  // audioUrl: { title: '单词音频文件',order: 7,view: 'file', type: 'string'},
  //子表高级查询
  inzWordCollocations: {
    title: '存储固定搭配',
    view: 'table',
    fields: {
      english: { title: '英文固定搭配', order: 1, view: 'text', type: 'string' },
      chinese: { title: '中文含义', order: 2, view: 'text', type: 'string' },
      type: { title: '类型', order: 3, view: 'text', type: 'string' },
    },
  },
  inzWordsEtymology: {
    title: '存储词源信息',
    view: 'table',
    fields: {
      originCh: { title: '词源中文解释', order: 1, view: 'text', type: 'string' },
      meaningEvolution: { title: '意义演变', order: 2, view: 'text', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
