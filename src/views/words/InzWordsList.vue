<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection" :expandedRowKeys="expandedRowKeys"  @expand="handleExpand">
      <!-- 内嵌table区域 begin -->
           <template #expandedRowRender="{record}">
             <a-tabs tabPosition="top">
               <a-tab-pane tab="存储固定搭配" key="inzWordCollocations" forceRender>
                  <inzWordCollocationsSubTable :id="expandedRowKeys[0]"/>
               </a-tab-pane>
               <a-tab-pane tab="存储词源信息" key="inzWordsEtymology" forceRender>
                  <inzWordsEtymologySubTable :id="expandedRowKeys[0]"/>
               </a-tab-pane>
             </a-tabs>
           </template>
     <!-- 内嵌table区域 end -->
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'words:inz_words:add'"  @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'words:inz_words:exportXls'"  preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button  type="primary" v-auth="'words:inz_words:importExcel'"  preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    删除
                  </a-menu-item>
                  <a-menu-item key="2" @click="batchHandleWordData">
                    AI解析
                  </a-menu-item>
                  <a-menu-item key="3" @click="batchHandleWordByUserData">
                    生成音频
                  </a-menu-item>
                  <a-menu-item key="4" @click="synchronousWords">
                    同步至相同单词
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button  v-auth="'words:inz_words:deleteBatch'">批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex==='audioUrl'">
          <!-- 文件字段回显插槽 -->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无音频</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:play-circle-outlined" size="small" @click="playAudio(text)">播放</a-button>
        </template>
        <template v-if="column.dataIndex==='usAudioUrl'">
          <!-- 文件字段回显插槽 -->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无音频</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:play-circle-outlined" size="small" @click="playAudio(text)">播放</a-button>
        </template>
        <template v-if="column.dataIndex==='naturalAudioUrl'">
          <!-- 文件字段回显插槽 -->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无音频</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:play-circle-outlined" size="small" @click="playAudio(text)">播放</a-button>
        </template>
        <template v-if="column.dataIndex==='breakdownAudioUrl'">
          <!-- 文件字段回显插槽 -->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无音频</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:play-circle-outlined" size="small" @click="playAudio(text)">播放</a-button>
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <InzWordsModal @register="registerModal" width="70%" @success="handleSuccess"></InzWordsModal>
  </div>
</template>

<script lang="ts" name="words-inzWords" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage'
  import {useModal} from '/@/components/Modal';
  import InzWordsModal from './components/InzWordsModal.vue'
  import InzWordCollocationsSubTable from './subTables/InzWordCollocationsSubTable.vue'
  import InzWordsEtymologySubTable from './subTables/InzWordsEtymologySubTable.vue'
  import {columns, searchFormSchema, superQuerySchema} from './InzWords.data';
  import {
    list,
    deleteOne,
    batchDelete,
    batchWordData,
    getImportUrl,
    getExportUrl,
    batchWordByUserData, synchronousWordsData
  } from "./InzWords.api";
  import {downloadFile} from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  const queryParam = reactive<any>({});
  // 展开key
  const expandedRowKeys = ref<any[]>([]);
  //注册model
  const [registerModal, {openModal}] = useModal();
  const [register3, { openModal: openModal3 }] = useModal();
  const userStore = useUserStore();
   //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '单词管理',
           api: list,
           columns,
           canResize:false,
           formConfig: {
                // labelWidth: 1200,
                schemas: searchFormSchema,
                autoSubmitOnEnter:true,
                showAdvancedButton:true,
                fieldMapToNumber: [
                ],
                fieldMapToTime: [
                ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
           },
           beforeFetch: (params) => {
             return Object.assign(params, queryParam);
           },
        },
        exportConfig: {
            name:"单词管理",
            url: getExportUrl,
            params: queryParam,
        },
        importConfig: {
            url: getImportUrl,
            success: handleSuccess
        },
    })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

   // 高级查询配置
   const superQueryConfig = reactive(superQuerySchema);

   /**
   * 高级查询事件
   */
   function handleSuperQuery(params) {
     Object.keys(params).map((k) => {
       queryParam[k] = params[k];
     });
     reload();
   }

   /**
     * 展开事件
     * */
   function handleExpand(expanded, record){
        expandedRowKeys.value=[];
        if (expanded === true) {
           expandedRowKeys.value.push(record.id)
        }
    }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value},handleSuccess);
   }
  /**
   * 批量解析单词事件
   */
  async function batchHandleWordData() {
    await batchWordData({ids: selectedRowKeys.value},handleSuccess);
  }
  async function batchHandleWordByUserData() {
    await batchWordByUserData({ids: selectedRowKeys.value},handleSuccess);
  }

  async function synchronousWords() {
    await synchronousWordsData({ids: selectedRowKeys.value},handleSuccess);
  }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'words:inz_words:edit'
         }
       ]
   }
  function playAudio(url: string) {
    const audio = new Audio(url);
    audio.play().catch(error => {
      console.error("音频播放失败", error);
    });
  }


  /**
   * 下拉操作栏
   */
  function getDropDownAction(record){
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft'
        },
        auth: 'words:inz_words:delete'
      }
    ]
  }

</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>
