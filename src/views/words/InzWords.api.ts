import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/words/inzWords/list',
  save='/words/inzWords/add',
  edit='/words/inzWords/edit',
  deleteOne = '/words/inzWords/delete',
  deleteBatch = '/words/inzWords/deleteBatch',
  handleBatch = '/words/inzWords/handleBatch',
  handleBatchByUser = '/words/inzWords/handleBatchByWords',
  synchronousWords = '/words/inzWords/synchronousWordsData',
  importExcel = '/words/inzWords/importExcel',
  exportXls = '/words/inzWords/exportXls',
  inzWordCollocationsList = '/words/inzWords/queryInzWordCollocationsByMainId',
  inzWordsEtymologyList = '/words/inzWords/queryInzWordsEtymologyByMainId',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzWordCollocations = Api.inzWordCollocationsList
/**
 * 子表单查询接口
 * @param params
 */
export const queryInzWordsEtymology = Api.inzWordsEtymologyList
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>{
  params.column = "";
  params.order = "";
  return defHttp.get({url: Api.list, params});
}


/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

export const batchWordData = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交解析',
    content: '如果有数据则会覆盖，是否解析选中数据。',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({url: Api.handleBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

export const batchWordByUserData = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交重新生成音频',
    content: '根据用户自行输入自然拼读与拆分单词生成音频，会覆盖当前音频，是否确认生成',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({url: Api.handleBatchByUser, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

export const synchronousWordsData = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交覆盖同名数据',
    content: '根据用户自行输入数据，会覆盖所有同名单词数据，是否确认提交',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.post({url: Api.synchronousWords, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
/**
 * 子表列表接口
 * @param params
 */
export const inzWordCollocationsList = (params) =>
  defHttp.get({url: Api.inzWordCollocationsList, params},{isTransformResponse:false});
/**
 * 子表列表接口
 * @param params
 */
export const inzWordsEtymologyList = (params) =>
  defHttp.get({url: Api.inzWordsEtymologyList, params},{isTransformResponse:false});
