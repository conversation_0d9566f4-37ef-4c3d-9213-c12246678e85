import { BasicColumn, FormSchema } from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '模块名称',
    align: 'center',
    dataIndex: 'moduleName',
  },
  {
    title: '模块编码',
    align: 'center',
    dataIndex: 'moduleCode',
  },
  {
    title: '模块描述',
    align: 'center',
    dataIndex: 'description',
  },
  {
    title: '封面图片URL',
    align: 'center',
    dataIndex: 'coverImage',
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortOrder',
  },
  {
    title: '状态 0-禁用 1-启用',
    align: 'center',
    dataIndex: 'status',
  },
  {
    title: '总视频数量',
    align: 'center',
    dataIndex: 'totalVideos',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    title: '模块名称',
    field: 'moduleName',
    component: 'Input',
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '模块名称',
    field: 'moduleName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入模块名称!' }];
    },
  },
  {
    label: '模块编码',
    field: 'moduleCode',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入模块编码!' }];
    },
  },
  {
    label: '模块描述',
    field: 'description',
    component: 'InputTextArea',
  },
  {
    label: '封面图片URL',
    field: 'coverImage',
    component: 'Input',
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
  },
  {
    label: '是否免费 0-收费 1-免费',
    field: 'isFree',
    component: 'InputNumber',
  },
  {
    label: '状态 0-禁用 1-启用',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '总视频数量',
    field: 'totalVideos',
    component: 'InputNumber',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  moduleName: { title: '模块名称', order: 0, view: 'text', type: 'string' },
  moduleCode: { title: '模块编码', order: 1, view: 'text', type: 'string' },
  description: { title: '模块描述', order: 2, view: 'textarea', type: 'string' },
  coverImage: { title: '封面图片URL', order: 3, view: 'text', type: 'string' },
  sortOrder: { title: '排序号', order: 4, view: 'number', type: 'number' },
  isFree: { title: '是否免费 0-收费 1-免费', order: 5, view: 'number', type: 'number' },
  status: { title: '状态 0-禁用 1-启用', order: 6, view: 'number', type: 'number' },
  totalVideos: { title: '总视频数量', order: 7, view: 'number', type: 'number' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
