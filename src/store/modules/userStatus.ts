import { defineStore } from 'pinia';
import { getToken } from '/@/utils/auth';

interface UserStatus {
  loginUserId: string;
  loginUsername: string;
  loginRealname: string;
  sysUserPhone: string;
  sysUserUsername: string;
  roleCodes: string[];
  roleDescription: string;
  isSystemAdmin: boolean;
  isAgentUser: boolean;
}

interface UserStatusState {
  userStatus: UserStatus | null;
  loading: boolean;
  error: string | null;
}

export const useUserStatusStore = defineStore('userStatus', {
  state: (): UserStatusState => ({
    userStatus: null,
    loading: false,
    error: null
  }),
  
  getters: {
    // 是否为系统管理员
    isSystemAdmin: (state) => state.userStatus?.isSystemAdmin || false,
    
    // 是否为代理商用户
    isAgentUser: (state) => state.userStatus?.isAgentUser || false,
    
    // 是否有用户管理权限
    hasUserManagePermission: (state) => 
      state.userStatus?.isSystemAdmin || state.userStatus?.isAgentUser,
    
    // 角色描述
    roleDescription: (state) => state.userStatus?.roleDescription || '未知角色',
    
    // 角色代码列表
    roleCodes: (state) => state.userStatus?.roleCodes || [],
    
    // 用户信息
    userInfo: (state) => ({
      username: state.userStatus?.loginUsername || '',
      realname: state.userStatus?.loginRealname || '',
      phone: state.userStatus?.sysUserPhone || ''
    }),

    // 页面标题
    pageTitle: (state) => {
      if (state.userStatus?.isSystemAdmin) return '用户管理 - 全部用户';
      if (state.userStatus?.isAgentUser) return '用户管理 - 我的推荐用户';
      return '用户列表';
    },

    // 角色标签类型
    roleTagType: (state) => {
      if (state.userStatus?.isSystemAdmin) return 'danger';
      if (state.userStatus?.isAgentUser) return 'warning';
      return 'info';
    }
  },
  
  actions: {
    // 获取用户状态
    async fetchUserStatus() {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('开始获取用户状态...');
        
        const response = await fetch('/jeecg-boot/user_front/inzUserFront/debugUserRole', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-Access-Token': getToken() || ''
          }
        });
        
        const result = await response.json();
        
        console.log('用户状态接口响应:', result);
        
        if (result.success) {
          this.userStatus = result.result;
          console.log('用户状态获取成功:', {
            roleDescription: result.result.roleDescription,
            isSystemAdmin: result.result.isSystemAdmin,
            isAgentUser: result.result.isAgentUser,
            roleCodes: result.result.roleCodes
          });
        } else {
          this.error = result.message || '获取用户状态失败';
          console.error('获取用户状态失败:', result.message);
        }
      } catch (error) {
        this.error = error.message || '网络请求失败';
        console.error('获取用户状态异常:', error);
      } finally {
        this.loading = false;
      }
    },
    
    // 清除用户状态
    clearUserStatus() {
      this.userStatus = null;
      this.error = null;
      console.log('用户状态已清除');
    },

    // 调试用户状态
    debugUserStatus() {
      console.log('=== 用户状态调试信息 ===');
      console.log('完整状态:', this.userStatus);
      console.log('是否管理员:', this.isSystemAdmin);
      console.log('是否代理商:', this.isAgentUser);
      console.log('有管理权限:', this.hasUserManagePermission);
      console.log('角色描述:', this.roleDescription);
      console.log('角色代码:', this.roleCodes);
      console.log('用户信息:', this.userInfo);
      console.log('==================');
      return this.userStatus;
    }
  }
});
